'use client'

import React from 'react'
import { useRouter } from 'next/navigation'

export default function EmergencyResetPage() {
  const router = useRouter()

  const handleFullReset = () => {
    console.log('🚨 FULL RESET: Clearing all data')
    
    // Очищаем все данные
    localStorage.clear()
    sessionStorage.clear()
    
    // Удаляем все cookies
    document.cookie.split(";").forEach((c) => {
      const eqPos = c.indexOf("=")
      const name = eqPos > -1 ? c.substr(0, eqPos) : c
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
    })
    
    // Создаем новый ID
    const newId = Date.now().toString()
    localStorage.setItem('telegram_user_id', newId)
    
    console.log('✅ Reset complete, new ID:', newId)
    
    // Перенаправляем на welcome
    window.location.href = '/welcome'
  }

  const handleSoftReset = () => {
    console.log('🔄 SOFT RESET: Clearing user data only')
    
    // Очищаем только пользовательские данные
    localStorage.removeItem('telegram_user_id')
    localStorage.removeItem('user_data')
    
    // Создаем новый ID
    const newId = Date.now().toString()
    localStorage.setItem('telegram_user_id', newId)
    
    console.log('✅ Soft reset complete, new ID:', newId)
    
    // Перенаправляем на главную
    window.location.href = '/'
  }

  const handleGoToWelcome = () => {
    console.log('👋 Going to welcome page')
    router.push('/welcome')
  }

  const handleGoToHome = () => {
    console.log('🏠 Going to home page')
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-2">
            🚨 Экстренный сброс
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-sm">
            Если приложение зависло или работает некорректно, используйте эти кнопки для сброса
          </p>
        </div>

        <div className="space-y-4">
          {/* Полный сброс */}
          <div className="border border-red-200 dark:border-red-800 rounded-lg p-4">
            <h3 className="font-semibold text-red-700 dark:text-red-300 mb-2">
              Полный сброс
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Удаляет ВСЕ данные (localStorage, cookies, etc.) и создает новый профиль
            </p>
            <button
              onClick={handleFullReset}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
            >
              🗑️ Полный сброс
            </button>
          </div>

          {/* Мягкий сброс */}
          <div className="border border-orange-200 dark:border-orange-800 rounded-lg p-4">
            <h3 className="font-semibold text-orange-700 dark:text-orange-300 mb-2">
              Мягкий сброс
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Удаляет только пользовательские данные, создает новый ID
            </p>
            <button
              onClick={handleSoftReset}
              className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              🔄 Мягкий сброс
            </button>
          </div>

          {/* Навигация */}
          <div className="border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="font-semibold text-blue-700 dark:text-blue-300 mb-2">
              Навигация
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Попробуйте перейти на другие страницы
            </p>
            <div className="space-y-2">
              <button
                onClick={handleGoToWelcome}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                👋 Страница приветствия
              </button>
              <button
                onClick={handleGoToHome}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                🏠 Главная страница
              </button>
            </div>
          </div>

          {/* Информация */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h3 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
              Текущие данные
            </h3>
            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
              <p>
                <strong>Telegram ID:</strong> {localStorage.getItem('telegram_user_id') || 'Не установлен'}
              </p>
              <p>
                <strong>Время:</strong> {new Date().toLocaleString()}
              </p>
              <p>
                <strong>URL:</strong> {window.location.href}
              </p>
            </div>
          </div>
        </div>

        {/* Дополнительные действия */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex space-x-2">
            <button
              onClick={() => window.location.reload()}
              className="flex-1 px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors"
            >
              🔄 Обновить
            </button>
            <button
              onClick={() => window.history.back()}
              className="flex-1 px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors"
            >
              ← Назад
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
