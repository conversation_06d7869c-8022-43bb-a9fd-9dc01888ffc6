'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { CheckCircle, XCircle, Clock, RotateCcw } from 'lucide-react'
import { triggerHapticFeedback } from '@/lib/telegram'

interface CardData {
  id: string
  text: string
  type: 'english' | 'russian'
  pairId: string
}

interface MemoryMatchGameProps {
  wordPairs: Array<{ english: string; russian: string }>
  timeLimit?: number // в секундах, по умолчанию 120 секунд (2 минуты)
  onComplete: (correct: boolean, score?: number) => void
}

export function MemoryMatchGame({ 
  wordPairs, 
  timeLimit = 120,
  onComplete 
}: MemoryMatchGameProps) {
  const [cards, setCards] = useState<CardData[]>([])
  const [selectedCards, setSelectedCards] = useState<string[]>([])
  const [matchedPairs, setMatchedPairs] = useState<string[]>([])
  const [wrongPairs, setWrongPairs] = useState<string[]>([])
  const [timeLeft, setTimeLeft] = useState(timeLimit)
  const [gameStarted, setGameStarted] = useState(false)
  const [gameEnded, setGameEnded] = useState(false)
  const [score, setScore] = useState(0)

  // Инициализация карточек
  useEffect(() => {
    const gameCards: CardData[] = []
    
    wordPairs.forEach((pair, index) => {
      const pairId = `pair-${index}`
      gameCards.push({
        id: `en-${index}`,
        text: pair.english,
        type: 'english',
        pairId
      })
      gameCards.push({
        id: `ru-${index}`,
        text: pair.russian,
        type: 'russian',
        pairId
      })
    })
    
    // Перемешиваем карточки
    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)
    setCards(shuffledCards)
  }, [wordPairs])

  // Таймер
  useEffect(() => {
    if (!gameStarted || gameEnded) return

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setGameEnded(true)
          onComplete(false, score)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [gameStarted, gameEnded, score, onComplete])

  // Проверка завершения игры
  useEffect(() => {
    if (matchedPairs.length === wordPairs.length && wordPairs.length > 0) {
      setGameEnded(true)
      const finalScore = Math.max(0, 100 - Math.floor((timeLimit - timeLeft) / 2))
      setScore(finalScore)
      setTimeout(() => {
        onComplete(true, finalScore)
      }, 1500)
    }
  }, [matchedPairs.length, wordPairs.length, timeLimit, timeLeft, onComplete])

  const startGame = () => {
    setGameStarted(true)
    triggerHapticFeedback('light')
  }

  const resetGame = () => {
    setSelectedCards([])
    setMatchedPairs([])
    setWrongPairs([])
    setTimeLeft(timeLimit)
    setGameStarted(false)
    setGameEnded(false)
    setScore(0)
    
    // Перемешиваем карточки заново
    const gameCards: CardData[] = []
    wordPairs.forEach((pair, index) => {
      const pairId = `pair-${index}`
      gameCards.push({
        id: `en-${index}`,
        text: pair.english,
        type: 'english',
        pairId
      })
      gameCards.push({
        id: `ru-${index}`,
        text: pair.russian,
        type: 'russian',
        pairId
      })
    })
    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)
    setCards(shuffledCards)
    
    triggerHapticFeedback('light')
  }

  const handleCardClick = useCallback((cardId: string) => {
    if (!gameStarted || gameEnded || selectedCards.includes(cardId) || matchedPairs.some(pairId => 
      cards.find(c => c.id === cardId)?.pairId === pairId
    )) {
      return
    }

    const newSelectedCards = [...selectedCards, cardId]
    setSelectedCards(newSelectedCards)

    if (newSelectedCards.length === 2) {
      const [firstCardId, secondCardId] = newSelectedCards
      const firstCard = cards.find(c => c.id === firstCardId)
      const secondCard = cards.find(c => c.id === secondCardId)

      if (firstCard && secondCard && firstCard.pairId === secondCard.pairId) {
        // Правильная пара
        setMatchedPairs(prev => [...prev, firstCard.pairId])
        setSelectedCards([])
        triggerHapticFeedback('light')
      } else {
        // Неправильная пара
        setWrongPairs([firstCardId, secondCardId])
        triggerHapticFeedback('heavy')
        
        setTimeout(() => {
          setSelectedCards([])
          setWrongPairs([])
        }, 1000)
      }
    }
  }, [gameStarted, gameEnded, selectedCards, matchedPairs, cards])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getCardStyle = (card: CardData) => {
    const isSelected = selectedCards.includes(card.id)
    const isMatched = matchedPairs.includes(card.pairId)
    const isWrong = wrongPairs.includes(card.id)
    
    let baseStyle = 'h-20 text-sm font-medium transition-all duration-200 '
    
    if (isMatched) {
      baseStyle += 'bg-green-100 border-green-300 text-green-800 opacity-50'
    } else if (isWrong) {
      baseStyle += 'bg-red-100 border-red-300 text-red-800'
    } else if (isSelected) {
      baseStyle += 'bg-gray-100 border-gray-400 text-gray-800'
    } else {
      baseStyle += 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
    }
    
    return baseStyle
  }

  if (!gameStarted) {
    return (
      <div className="space-y-6 text-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Игра на сопоставление слов
          </h2>
          <p className="text-gray-600 mb-4">
            Найдите пары: английские слова и их русские переводы
          </p>
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500 mb-6">
            <Clock className="h-4 w-4" />
            <span>Время: {formatTime(timeLimit)}</span>
          </div>
        </div>
        
        <Button onClick={startGame} className="px-8 py-3">
          Начать игру
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Заголовок и таймер */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          Найдите пары слов
        </h2>
        <div className="flex items-center gap-4">
          <div className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${
            timeLeft <= 30 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'
          }`}>
            <Clock className="h-4 w-4" />
            <span>{formatTime(timeLeft)}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={resetGame}
            className="p-2"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Прогресс */}
      <div className="text-center text-sm text-gray-600">
        Найдено пар: {matchedPairs.length} из {wordPairs.length}
      </div>

      {/* Игровое поле */}
      <div className="grid grid-cols-2 gap-3">
        {cards.map((card) => (
          <Button
            key={card.id}
            variant="outline"
            className={getCardStyle(card)}
            onClick={() => handleCardClick(card.id)}
            disabled={gameEnded || matchedPairs.includes(card.pairId)}
          >
            <div className="text-center">
              <div className="text-xs text-gray-500 mb-1">
                {card.type === 'english' ? 'EN' : 'RU'}
              </div>
              <div>{card.text}</div>
            </div>
          </Button>
        ))}
      </div>

      {/* Результат игры */}
      {gameEnded && (
        <Card className={`${matchedPairs.length === wordPairs.length ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              {matchedPairs.length === wordPairs.length ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <XCircle className="h-8 w-8 text-red-600" />
              )}
            </div>
            <h3 className={`font-semibold mb-1 ${
              matchedPairs.length === wordPairs.length ? 'text-green-800' : 'text-red-800'
            }`}>
              {matchedPairs.length === wordPairs.length ? 'Отлично!' : 'Время вышло!'}
            </h3>
            <p className={`text-sm ${
              matchedPairs.length === wordPairs.length ? 'text-green-700' : 'text-red-700'
            }`}>
              {matchedPairs.length === wordPairs.length
                ? `Вы нашли все пары! Счёт: ${score}`
                : `Найдено пар: ${matchedPairs.length} из ${wordPairs.length}`
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
