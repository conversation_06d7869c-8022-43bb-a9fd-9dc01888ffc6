{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/emergency-reset/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useRouter } from 'next/navigation'\n\nexport default function EmergencyResetPage() {\n  const router = useRouter()\n\n  const handleFullReset = () => {\n    console.log('🚨 FULL RESET: Clearing all data')\n    \n    // Очищаем все данные\n    localStorage.clear()\n    sessionStorage.clear()\n    \n    // Удаляем все cookies\n    document.cookie.split(\";\").forEach((c) => {\n      const eqPos = c.indexOf(\"=\")\n      const name = eqPos > -1 ? c.substr(0, eqPos) : c\n      document.cookie = name + \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/\"\n    })\n    \n    // Создаем новый ID\n    const newId = Date.now().toString()\n    localStorage.setItem('telegram_user_id', newId)\n    \n    console.log('✅ Reset complete, new ID:', newId)\n    \n    // Перенаправляем на welcome\n    window.location.href = '/welcome'\n  }\n\n  const handleSoftReset = () => {\n    console.log('🔄 SOFT RESET: Clearing user data only')\n    \n    // Очищаем только пользовательские данные\n    localStorage.removeItem('telegram_user_id')\n    localStorage.removeItem('user_data')\n    \n    // Создаем новый ID\n    const newId = Date.now().toString()\n    localStorage.setItem('telegram_user_id', newId)\n    \n    console.log('✅ Soft reset complete, new ID:', newId)\n    \n    // Перенаправляем на главную\n    window.location.href = '/'\n  }\n\n  const handleGoToWelcome = () => {\n    console.log('👋 Going to welcome page')\n    router.push('/welcome')\n  }\n\n  const handleGoToHome = () => {\n    console.log('🏠 Going to home page')\n    router.push('/')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"text-center mb-6\">\n          <h1 className=\"text-2xl font-bold text-red-600 dark:text-red-400 mb-2\">\n            🚨 Экстренный сброс\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\n            Если приложение зависло или работает некорректно, используйте эти кнопки для сброса\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          {/* Полный сброс */}\n          <div className=\"border border-red-200 dark:border-red-800 rounded-lg p-4\">\n            <h3 className=\"font-semibold text-red-700 dark:text-red-300 mb-2\">\n              Полный сброс\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n              Удаляет ВСЕ данные (localStorage, cookies, etc.) и создает новый профиль\n            </p>\n            <button\n              onClick={handleFullReset}\n              className=\"w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium\"\n            >\n              🗑️ Полный сброс\n            </button>\n          </div>\n\n          {/* Мягкий сброс */}\n          <div className=\"border border-orange-200 dark:border-orange-800 rounded-lg p-4\">\n            <h3 className=\"font-semibold text-orange-700 dark:text-orange-300 mb-2\">\n              Мягкий сброс\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n              Удаляет только пользовательские данные, создает новый ID\n            </p>\n            <button\n              onClick={handleSoftReset}\n              className=\"w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium\"\n            >\n              🔄 Мягкий сброс\n            </button>\n          </div>\n\n          {/* Навигация */}\n          <div className=\"border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n            <h3 className=\"font-semibold text-blue-700 dark:text-blue-300 mb-2\">\n              Навигация\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n              Попробуйте перейти на другие страницы\n            </p>\n            <div className=\"space-y-2\">\n              <button\n                onClick={handleGoToWelcome}\n                className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n              >\n                👋 Страница приветствия\n              </button>\n              <button\n                onClick={handleGoToHome}\n                className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium\"\n              >\n                🏠 Главная страница\n              </button>\n            </div>\n          </div>\n\n          {/* Информация */}\n          <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n            <h3 className=\"font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n              Текущие данные\n            </h3>\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 space-y-1\">\n              <p>\n                <strong>Telegram ID:</strong> {localStorage.getItem('telegram_user_id') || 'Не установлен'}\n              </p>\n              <p>\n                <strong>Время:</strong> {new Date().toLocaleString()}\n              </p>\n              <p>\n                <strong>URL:</strong> {window.location.href}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Дополнительные действия */}\n        <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => window.location.reload()}\n              className=\"flex-1 px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors\"\n            >\n              🔄 Обновить\n            </button>\n            <button\n              onClick={() => window.history.back()}\n              className=\"flex-1 px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors\"\n            >\n              ← Назад\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;QAEZ,qBAAqB;QACrB,aAAa,KAAK;QAClB,eAAe,KAAK;QAEpB,sBAAsB;QACtB,SAAS,MAAM,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC;YAClC,MAAM,QAAQ,EAAE,OAAO,CAAC;YACxB,MAAM,OAAO,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,SAAS;YAC/C,SAAS,MAAM,GAAG,OAAO;QAC3B;QAEA,mBAAmB;QACnB,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;QACjC,aAAa,OAAO,CAAC,oBAAoB;QAEzC,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,4BAA4B;QAC5B,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;QAEZ,yCAAyC;QACzC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,mBAAmB;QACnB,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;QACjC,aAAa,OAAO,CAAC,oBAAoB;QAEzC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,4BAA4B;QAC5B,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAK1D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;8DAAO;;;;;;gDAAqB;gDAAE,aAAa,OAAO,CAAC,uBAAuB;;;;;;;sDAE7E,6LAAC;;8DACC,6LAAC;8DAAO;;;;;;gDAAe;gDAAE,IAAI,OAAO,cAAc;;;;;;;sDAEpD,6LAAC;;8DACC,6LAAC;8DAAO;;;;;;gDAAa;gDAAE,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;8BAOnD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;gCAClC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAlKwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}