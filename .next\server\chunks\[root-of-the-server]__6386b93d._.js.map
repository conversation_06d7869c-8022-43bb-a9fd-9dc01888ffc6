{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/api/lessons/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nconst supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// GET - получить все уроки\nexport async function GET() {\n  try {\n    const { data: lessons, error } = await supabase\n      .from('lessons')\n      .select('*')\n      .order('level, order')\n\n    if (error) {\n      console.error('Error fetching lessons:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ lessons })\n  } catch (error) {\n    console.error('Error in GET /api/lessons:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\n// POST - создать новый урок\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { title, level, order, description } = body\n\n    // Валидация\n    if (!title || !level || !order || !description) {\n      return NextResponse.json(\n        { error: 'All fields are required: title, level, order, description' },\n        { status: 400 }\n      )\n    }\n\n    // Проверяем, не существует ли уже урок с таким порядком в этом уровне\n    const { data: existingLesson } = await supabase\n      .from('lessons')\n      .select('id')\n      .eq('level', level)\n      .eq('order', order)\n      .single()\n\n    if (existingLesson) {\n      return NextResponse.json(\n        { error: `Lesson with order ${order} already exists for level ${level}` },\n        { status: 400 }\n      )\n    }\n\n    // Создаем урок\n    const { data: lesson, error } = await supabase\n      .from('lessons')\n      .insert([{\n        title,\n        level,\n        order: parseInt(order),\n        description\n      }])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating lesson:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ lesson }, { status: 201 })\n  } catch (error) {\n    console.error('Error in POST /api/lessons:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAGpC,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAQ;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG;QAE7C,YAAY;QACZ,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4D,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,sEAAsE;QACtE,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,SAAS,OACZ,MAAM;QAET,IAAI,gBAAgB;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,kBAAkB,EAAE,MAAM,0BAA0B,EAAE,OAAO;YAAC,GACxE;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;gBACP;gBACA;gBACA,OAAO,SAAS;gBAChB;YACF;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAO,GAAG;YAAE,QAAQ;QAAI;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}