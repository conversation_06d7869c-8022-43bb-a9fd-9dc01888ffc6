{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nconst supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// GET - получить пользователя по telegram_id\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const telegramId = searchParams.get('telegram_id')\n\n    if (!telegramId) {\n      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })\n    }\n\n    const { data: user, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('telegram_id', telegramId)\n      .single()\n\n    if (error && error.code !== 'PGRST116') {\n      console.error('Error fetching user:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ user: user || null })\n  } catch (error) {\n    console.error('Error in GET /api/users:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\n// POST - создать нового пользователя\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { telegramId, nickname, avatar, level, theme = 'light' } = body\n\n    // Валидация\n    if (!telegramId || !nickname || !avatar || !level) {\n      return NextResponse.json(\n        { error: 'All fields are required: telegramId, nickname, avatar, level' },\n        { status: 400 }\n      )\n    }\n\n    // Проверяем, не занят ли никнейм\n    const { data: existingUser } = await supabase\n      .from('users')\n      .select('id')\n      .eq('nickname', nickname)\n      .single()\n\n    if (existingUser) {\n      return NextResponse.json(\n        { error: 'Nickname is already taken' },\n        { status: 409 }\n      )\n    }\n\n    // Создаем пользователя\n    const { data: user, error } = await supabase\n      .from('users')\n      .insert([{\n        telegram_id: telegramId,\n        nickname,\n        avatar,\n        level,\n        theme,\n        is_onboarded: true,\n        total_xp: 0,\n        current_streak: 0,\n        last_activity_date: new Date().toISOString().split('T')[0]\n      }])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating user:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    // Настройки пользователя будут добавлены позже при необходимости\n\n    return NextResponse.json({ user }, { status: 201 })\n  } catch (error) {\n    console.error('Error in POST /api/users:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\n// PUT - обновить пользователя\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { telegramId, ...updateData } = body\n\n    if (!telegramId) {\n      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })\n    }\n\n    const { data: user, error } = await supabase\n      .from('users')\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString()\n      })\n      .eq('telegram_id', telegramId)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ user })\n  } catch (error) {\n    console.error('Error in PUT /api/users:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAGpC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;YACtC,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM,QAAQ;QAAK;IAChD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,OAAO,EAAE,GAAG;QAEjE,YAAY;QACZ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO;YACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+D,GACxE;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY,UACf,MAAM;QAET,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;gBACP,aAAa;gBACb;gBACA;gBACA;gBACA;gBACA,cAAc;gBACd,UAAU;gBACV,gBAAgB;gBAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5D;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,iEAAiE;QAEjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK,GAAG;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QAEtC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC;YACN,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,eAAe,YAClB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}