{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nconst supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// GET - получить пользователя по telegram_id\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const telegramId = searchParams.get('telegram_id')\n\n    if (!telegramId) {\n      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })\n    }\n\n    const { data: user, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('telegram_id', parseInt(telegramId) || telegramId)\n      .single()\n\n    if (error && error.code !== 'PGRST116') {\n      console.error('Error fetching user:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    // Если пользователь найден, форматируем его данные\n    if (user) {\n      const formattedUser = {\n        id: user.id,\n        telegram_id: user.telegram_id,\n        nickname: user.username || 'User',\n        avatar: user.first_name || '👤',\n        level: user.level,\n        theme: 'light',\n        is_onboarded: true,\n        total_xp: user.xp || 0,\n        current_streak: 0,\n        last_activity_date: new Date().toISOString().split('T')[0],\n        created_at: user.created_at,\n        updated_at: user.updated_at\n      }\n      return NextResponse.json({ user: formattedUser })\n    }\n\n    return NextResponse.json({ user: null })\n  } catch (error) {\n    console.error('Error in GET /api/users:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\n// POST - создать нового пользователя\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { telegramId, nickname, avatar, level, theme = 'light' } = body\n\n    // Валидация\n    if (!telegramId || !nickname || !avatar || !level) {\n      return NextResponse.json(\n        { error: 'All fields are required: telegramId, nickname, avatar, level' },\n        { status: 400 }\n      )\n    }\n\n    // Проверяем, не существует ли уже пользователь с таким telegram_id\n    const { data: existingUser } = await supabase\n      .from('users')\n      .select('*')\n      .eq('telegram_id', parseInt(telegramId) || telegramId)\n      .single()\n\n    if (existingUser) {\n      // Если пользователь существует, обновляем его данные вместо создания нового\n      const { data: updatedUser, error: updateError } = await supabase\n        .from('users')\n        .update({\n          username: nickname,\n          first_name: avatar,\n          level,\n          xp: existingUser.xp || 0,\n          updated_at: new Date().toISOString()\n        })\n        .eq('telegram_id', parseInt(telegramId) || telegramId)\n        .select()\n        .single()\n\n      if (updateError) {\n        console.error('Error updating user:', updateError)\n        return NextResponse.json({ error: updateError.message }, { status: 500 })\n      }\n\n      // Возвращаем обновленного пользователя в ожидаемом формате\n      const formattedUser = {\n        id: updatedUser.id,\n        telegram_id: updatedUser.telegram_id,\n        nickname: updatedUser.username,\n        avatar: updatedUser.first_name,\n        level: updatedUser.level,\n        theme: theme,\n        is_onboarded: true,\n        total_xp: updatedUser.xp,\n        current_streak: 0,\n        last_activity_date: new Date().toISOString().split('T')[0],\n        created_at: updatedUser.created_at,\n        updated_at: updatedUser.updated_at\n      }\n\n      return NextResponse.json({ user: formattedUser }, { status: 200 })\n    }\n\n    // Создаем пользователя с существующими колонками\n    const { data: user, error } = await supabase\n      .from('users')\n      .insert([{\n        telegram_id: parseInt(telegramId) || Date.now(), // Конвертируем в число\n        username: nickname, // Используем username вместо nickname\n        first_name: avatar, // Временно сохраняем аватар в first_name\n        level,\n        xp: 0\n      }])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating user:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    // Возвращаем пользователя в ожидаемом формате\n    const formattedUser = {\n      id: user.id,\n      telegram_id: user.telegram_id,\n      nickname: user.username,\n      avatar: user.first_name,\n      level: user.level,\n      theme: theme,\n      is_onboarded: true,\n      total_xp: user.xp,\n      current_streak: 0,\n      last_activity_date: new Date().toISOString().split('T')[0],\n      created_at: user.created_at,\n      updated_at: user.updated_at\n    }\n\n    return NextResponse.json({ user: formattedUser }, { status: 201 })\n  } catch (error) {\n    console.error('Error in POST /api/users:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\n// PUT - обновить пользователя\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { telegramId, nickname, avatar, level, theme, total_xp, ...otherData } = body\n\n    if (!telegramId) {\n      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })\n    }\n\n    // Маппим новые поля на существующие колонки\n    const updateFields: any = {\n      updated_at: new Date().toISOString()\n    }\n\n    if (nickname) updateFields.username = nickname\n    if (avatar) updateFields.first_name = avatar\n    if (level) updateFields.level = level\n    if (total_xp !== undefined) updateFields.xp = total_xp\n\n    const { data: user, error } = await supabase\n      .from('users')\n      .update(updateFields)\n      .eq('telegram_id', parseInt(telegramId) || telegramId)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    // Форматируем ответ\n    const formattedUser = {\n      id: user.id,\n      telegram_id: user.telegram_id,\n      nickname: user.username,\n      avatar: user.first_name,\n      level: user.level,\n      theme: theme || 'light',\n      is_onboarded: true,\n      total_xp: user.xp,\n      current_streak: 0,\n      last_activity_date: new Date().toISOString().split('T')[0],\n      created_at: user.created_at,\n      updated_at: user.updated_at\n    }\n\n    return NextResponse.json({ user: formattedUser })\n  } catch (error) {\n    console.error('Error in PUT /api/users:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAGpC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,SAAS,eAAe,YAC1C,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;YACtC,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,mDAAmD;QACnD,IAAI,MAAM;YACR,MAAM,gBAAgB;gBACpB,IAAI,KAAK,EAAE;gBACX,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,QAAQ,KAAK,UAAU,IAAI;gBAC3B,OAAO,KAAK,KAAK;gBACjB,OAAO;gBACP,cAAc;gBACd,UAAU,KAAK,EAAE,IAAI;gBACrB,gBAAgB;gBAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1D,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;YAC7B;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAc;QACjD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAK;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,OAAO,EAAE,GAAG;QAEjE,YAAY;QACZ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO;YACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+D,GACxE;gBAAE,QAAQ;YAAI;QAElB;QAEA,mEAAmE;QACnE,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,SAAS,eAAe,YAC1C,MAAM;QAET,IAAI,cAAc;YAChB,4EAA4E;YAC5E,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,UAAU;gBACV,YAAY;gBACZ;gBACA,IAAI,aAAa,EAAE,IAAI;gBACvB,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,eAAe,SAAS,eAAe,YAC1C,MAAM,GACN,MAAM;YAET,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO,YAAY,OAAO;gBAAC,GAAG;oBAAE,QAAQ;gBAAI;YACzE;YAEA,2DAA2D;YAC3D,MAAM,gBAAgB;gBACpB,IAAI,YAAY,EAAE;gBAClB,aAAa,YAAY,WAAW;gBACpC,UAAU,YAAY,QAAQ;gBAC9B,QAAQ,YAAY,UAAU;gBAC9B,OAAO,YAAY,KAAK;gBACxB,OAAO;gBACP,cAAc;gBACd,UAAU,YAAY,EAAE;gBACxB,gBAAgB;gBAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1D,YAAY,YAAY,UAAU;gBAClC,YAAY,YAAY,UAAU;YACpC;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAc,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,iDAAiD;QACjD,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;gBACP,aAAa,SAAS,eAAe,KAAK,GAAG;gBAC7C,UAAU;gBACV,YAAY;gBACZ;gBACA,IAAI;YACN;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,8CAA8C;QAC9C,MAAM,gBAAgB;YACpB,IAAI,KAAK,EAAE;YACX,aAAa,KAAK,WAAW;YAC7B,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,UAAU;YACvB,OAAO,KAAK,KAAK;YACjB,OAAO;YACP,cAAc;YACd,UAAU,KAAK,EAAE;YACjB,gBAAgB;YAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAc,GAAG;YAAE,QAAQ;QAAI;IAClE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,GAAG;QAE/E,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,4CAA4C;QAC5C,MAAM,eAAoB;YACxB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,IAAI,UAAU,aAAa,QAAQ,GAAG;QACtC,IAAI,QAAQ,aAAa,UAAU,GAAG;QACtC,IAAI,OAAO,aAAa,KAAK,GAAG;QAChC,IAAI,aAAa,WAAW,aAAa,EAAE,GAAG;QAE9C,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,cACP,EAAE,CAAC,eAAe,SAAS,eAAe,YAC1C,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,oBAAoB;QACpB,MAAM,gBAAgB;YACpB,IAAI,KAAK,EAAE;YACX,aAAa,KAAK,WAAW;YAC7B,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,UAAU;YACvB,OAAO,KAAK,KAAK;YACjB,OAAO,SAAS;YAChB,cAAc;YACd,UAAU,KAAK,EAAE;YACjB,gBAAgB;YAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAc;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}