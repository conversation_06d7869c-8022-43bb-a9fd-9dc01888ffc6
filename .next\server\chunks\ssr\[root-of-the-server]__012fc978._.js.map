{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/test-simple/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useUser } from '@/contexts/UserContext'\nimport { useTheme } from '@/contexts/ThemeContext'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { Sun, Moon, User, Home } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function TestSimplePage() {\n  const { user, isLoading } = useUser()\n  const { theme, toggleTheme } = useTheme()\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-300\">Загружаем...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 p-4\">\n      <div className=\"max-w-md mx-auto space-y-6\">\n        {/* Header */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Тестовая страница\n              </h1>\n              <div className=\"flex items-center space-x-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={toggleTheme}\n                  className=\"p-2\"\n                >\n                  {theme === 'light' ? (\n                    <Moon className=\"h-4 w-4\" />\n                  ) : (\n                    <Sun className=\"h-4 w-4\" />\n                  )}\n                </Button>\n                <Link href=\"/\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"p-2\">\n                    <Home className=\"h-4 w-4\" />\n                  </Button>\n                </Link>\n              </div>\n            </div>\n          </CardHeader>\n        </Card>\n\n        {/* User Info */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Информация о пользователе\n            </h2>\n          </CardHeader>\n          <CardContent>\n            {user ? (\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"text-2xl\">{user.avatar}</div>\n                  <div>\n                    <p className=\"font-medium text-gray-900 dark:text-white\">\n                      {user.nickname}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Уровень: {user.level}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"grid grid-cols-2 gap-4 text-center\">\n                  <div>\n                    <div className=\"text-lg font-bold text-blue-600 dark:text-blue-400\">\n                      {user.total_xp}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-300\">XP</div>\n                  </div>\n                  <div>\n                    <div className=\"text-lg font-bold text-green-600 dark:text-green-400\">\n                      {user.current_streak}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-300\">Дней</div>\n                  </div>\n                </div>\n                <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-4\">\n                  <p>ID: {user.id}</p>\n                  <p>Telegram ID: {user.telegram_id}</p>\n                  <p>Тема: {user.theme}</p>\n                  <p>Зарегистрирован: {user.is_onboarded ? 'Да' : 'Нет'}</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"text-center py-4\">\n                <User className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Пользователь не найден\n                </p>\n                <Link href=\"/welcome\">\n                  <Button className=\"mt-3\">\n                    Зарегистрироваться\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Theme Test */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Тест темы\n            </h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                Текущая тема: <span className=\"font-medium\">{theme}</span>\n              </p>\n              <Button onClick={toggleTheme} className=\"w-full\">\n                Переключить тему\n              </Button>\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900 rounded text-center\">\n                  <p className=\"text-blue-800 dark:text-blue-200 text-sm\">Синий</p>\n                </div>\n                <div className=\"p-3 bg-green-100 dark:bg-green-900 rounded text-center\">\n                  <p className=\"text-green-800 dark:text-green-200 text-sm\">Зеленый</p>\n                </div>\n                <div className=\"p-3 bg-red-100 dark:bg-red-900 rounded text-center\">\n                  <p className=\"text-red-800 dark:text-red-200 text-sm\">Красный</p>\n                </div>\n                <div className=\"p-3 bg-purple-100 dark:bg-purple-900 rounded text-center\">\n                  <p className=\"text-purple-800 dark:text-purple-200 text-sm\">Фиолетовый</p>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Navigation */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Навигация\n            </h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              <Link href=\"/\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Главная страница\n                </Button>\n              </Link>\n              <Link href=\"/admin\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Админ панель\n                </Button>\n              </Link>\n              <Link href=\"/test-memory-match\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Тест Memory Match\n                </Button>\n              </Link>\n              <Link href=\"/welcome\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Страница приветствия\n                </Button>\n              </Link>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAGnB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAC5C,cAAA,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5B,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;sCAItE,8OAAC,gIAAA,CAAA,cAAW;sCACT,qBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAY,KAAK,MAAM;;;;;;0DACtC,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,KAAK,QAAQ;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;;4DAA2C;4DAC5C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;kDAI1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ;;;;;;kEAEhB,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,KAAK,cAAc;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAE;oDAAK,KAAK,EAAE;;;;;;;0DACf,8OAAC;;oDAAE;oDAAc,KAAK,WAAW;;;;;;;0DACjC,8OAAC;;oDAAE;oDAAO,KAAK,KAAK;;;;;;;0DACpB,8OAAC;;oDAAE;oDAAkB,KAAK,YAAY,GAAG,OAAO;;;;;;;;;;;;;;;;;;qDAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnC,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;sCAItE,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAmC;0DAChC,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE/C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAa,WAAU;kDAAS;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;sCAItE,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;kDAI/C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;kDAI/C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;kDAI/C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D", "debugId": null}}]}