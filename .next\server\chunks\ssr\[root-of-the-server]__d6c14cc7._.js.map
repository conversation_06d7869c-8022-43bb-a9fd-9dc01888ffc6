{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LevelBadge.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LevelBadgeProps {\n  level: string\n  className?: string\n}\n\nconst levelColors = {\n  'A1': 'bg-green-100 text-green-800 border-green-200',\n  'A2': 'bg-blue-100 text-blue-800 border-blue-200',\n  'B1': 'bg-yellow-100 text-yellow-800 border-yellow-200',\n  'B2': 'bg-orange-100 text-orange-800 border-orange-200',\n  'C1': 'bg-red-100 text-red-800 border-red-200',\n  'C2': 'bg-purple-100 text-purple-800 border-purple-200',\n}\n\nexport function LevelBadge({ level, className }: LevelBadgeProps) {\n  const colorClass = levelColors[level as keyof typeof levelColors] || levelColors['A1']\n  \n  return (\n    <span\n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        colorClass,\n        className\n      )}\n    >\n      {level}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,MAAM,aAAa,WAAW,CAAC,MAAkC,IAAI,WAAW,CAAC,KAAK;IAEtF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,YACA;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/lessons/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\nimport { ArrowLeft, BookO<PERSON>, Clock, Trophy } from 'lucide-react'\nimport { getLessonsByLevel } from '@/lib/database'\nimport { Database } from '@/lib/database.types'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { LevelBadge } from '@/components/ui/LevelBadge'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\n\ntype Lesson = Database['public']['Tables']['lessons']['Row']\n\nexport default function LessonsPage() {\n  const searchParams = useSearchParams()\n  const level = searchParams.get('level') || 'A1'\n  const [lessons, setLessons] = useState<Lesson[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadLessons()\n  }, [level])\n\n  const loadLessons = async () => {\n    try {\n      setLoading(true)\n      const levelLessons = await getLessonsByLevel(level)\n      setLessons(levelLessons)\n    } catch (error) {\n      console.error('Error loading lessons:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-md mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4\" />\n              </Button>\n            </Link>\n            <div className=\"flex-1\">\n              <div className=\"flex items-center space-x-2\">\n                <LevelBadge level={level} />\n                <h1 className=\"text-lg font-semibold text-gray-900\">\n                  Уроки уровня {level}\n                </h1>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-md mx-auto px-4 py-6\">\n        {loading ? (\n          <div className=\"flex justify-center py-8\">\n            <LoadingSpinner size=\"lg\" />\n          </div>\n        ) : lessons.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h2 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Нет доступных уроков\n            </h2>\n            <p className=\"text-gray-600\">\n              Уроки для уровня {level} скоро появятся!\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {lessons.map((lesson, index) => (\n              <Card key={lesson.id} className=\"hover:shadow-md transition-shadow\">\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2 mb-1\">\n                        <span className=\"text-sm font-medium text-blue-600\">\n                          Урок {lesson.order}\n                        </span>\n                      </div>\n                      <h3 className=\"font-semibold text-gray-900 mb-1\">\n                        {lesson.title}\n                      </h3>\n                      {lesson.description && (\n                        <p className=\"text-sm text-gray-600\">\n                          {lesson.description}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"pt-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"h-4 w-4\" />\n                        <span>~10 мин</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Trophy className=\"h-4 w-4\" />\n                        <span>50 XP</span>\n                      </div>\n                    </div>\n                    <Link href={`/lesson/${lesson.id}`}>\n                      <Button size=\"sm\">\n                        Начать урок\n                      </Button>\n                    </Link>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,QAAQ,aAAa,GAAG,CAAC,YAAY;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,eAAe,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;YAC7C,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sIAAA,CAAA,aAAU;4CAAC,OAAO;;;;;;sDACnB,8OAAC;4CAAG,WAAU;;gDAAsC;gDACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1B,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;;;;;2BAErB,QAAQ,MAAM,KAAK,kBACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;;gCAAgB;gCACT;gCAAM;;;;;;;;;;;;yCAI5B,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,gIAAA,CAAA,OAAI;4BAAiB,WAAU;;8CAC9B,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;;4DAAoC;4DAC5C,OAAO,KAAK;;;;;;;;;;;;8DAGtB,8OAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;gDAEd,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;8CAM7B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;0DAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;8DAAK;;;;;;;;;;;;;;;;;;;;;;;2BAjCf,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;AA8ClC", "debugId": null}}]}