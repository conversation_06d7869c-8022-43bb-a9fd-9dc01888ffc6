{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA7BgB", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;KAbgB;AAeT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/onboarding/nickname/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { User, Check, X, Loader2 } from 'lucide-react'\nimport { useRouter } from 'next/navigation'\n\nconst AVATARS = [\n  '👤', '🧑', '👩', '🧔', '👱', '🧑‍🦰', '👩‍🦰', '🧑‍🦱', '👩‍🦱', '🧑‍🦲',\n  '👩‍🦲', '🧓', '👴', '👵', '🙂', '😊', '😎', '🤓', '🥸', '🤠'\n]\n\nexport default function NicknamePage() {\n  const router = useRouter()\n  const [nickname, setNickname] = useState('')\n  const [selectedAvatar, setSelectedAvatar] = useState(AVATARS[0])\n  const [isChecking, setIsChecking] = useState(false)\n  const [nicknameStatus, setNicknameStatus] = useState<'idle' | 'available' | 'taken' | 'invalid'>('idle')\n  const [errorMessage, setErrorMessage] = useState('')\n\n  // Проверка никнейма с задержкой\n  useEffect(() => {\n    if (nickname.length < 3) {\n      setNicknameStatus('idle')\n      return\n    }\n\n    const timeoutId = setTimeout(async () => {\n      await checkNickname(nickname)\n    }, 500)\n\n    return () => clearTimeout(timeoutId)\n  }, [nickname])\n\n  const checkNickname = async (nicknameToCheck: string) => {\n    setIsChecking(true)\n    try {\n      const telegramId = localStorage.getItem('telegram_user_id')\n      const url = `/api/users/check-nickname?nickname=${encodeURIComponent(nicknameToCheck)}${telegramId ? `&telegram_id=${telegramId}` : ''}`\n      const response = await fetch(url)\n      const data = await response.json()\n      \n      if (data.available) {\n        setNicknameStatus('available')\n        setErrorMessage('')\n      } else {\n        setNicknameStatus(data.error.includes('characters') || data.error.includes('contain') ? 'invalid' : 'taken')\n        setErrorMessage(data.error)\n      }\n    } catch (error) {\n      console.error('Error checking nickname:', error)\n      setNicknameStatus('invalid')\n      setErrorMessage('Ошибка проверки никнейма')\n    } finally {\n      setIsChecking(false)\n    }\n  }\n\n  const handleNext = () => {\n    if (nicknameStatus === 'available' && selectedAvatar) {\n      // Сохраняем данные в localStorage для следующего шага\n      localStorage.setItem('onboarding_nickname', nickname)\n      localStorage.setItem('onboarding_avatar', selectedAvatar)\n      router.push('/onboarding/level')\n    }\n  }\n\n  const getNicknameInputStyle = () => {\n    if (isChecking) return 'border-yellow-300 focus:ring-yellow-500'\n    if (nicknameStatus === 'available') return 'border-green-300 focus:ring-green-500'\n    if (nicknameStatus === 'taken' || nicknameStatus === 'invalid') return 'border-red-300 focus:ring-red-500'\n    return 'border-gray-300 focus:ring-blue-500'\n  }\n\n  const getNicknameIcon = () => {\n    if (isChecking) return <Loader2 className=\"h-5 w-5 text-yellow-500 animate-spin\" />\n    if (nicknameStatus === 'available') return <Check className=\"h-5 w-5 text-green-500\" />\n    if (nicknameStatus === 'taken' || nicknameStatus === 'invalid') return <X className=\"h-5 w-5 text-red-500\" />\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full\">\n        <Card className=\"shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\">\n          <CardHeader className=\"text-center pb-4\">\n            <div className=\"mb-4\">\n              <User className=\"h-12 w-12 text-purple-600 dark:text-purple-400 mx-auto\" />\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Создай свой профиль\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Выбери никнейм и аватар\n            </p>\n          </CardHeader>\n\n          <CardContent className=\"space-y-6\">\n            {/* Никнейм */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Никнейм\n              </label>\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  value={nickname}\n                  onChange={(e) => setNickname(e.target.value.toLowerCase().replace(/[^a-z0-9_-]/g, ''))}\n                  className={`w-full px-4 py-3 pr-12 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${getNicknameInputStyle()} dark:bg-gray-700 dark:text-white`}\n                  placeholder=\"Введи свой никнейм\"\n                  maxLength={20}\n                />\n                <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n                  {getNicknameIcon()}\n                </div>\n              </div>\n              {errorMessage && (\n                <p className=\"text-sm text-red-600 dark:text-red-400 mt-1\">{errorMessage}</p>\n              )}\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                3-20 символов, только буквы, цифры, _ и -\n              </p>\n            </div>\n\n            {/* Выбор аватара */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                Выбери аватар\n              </label>\n              <div className=\"grid grid-cols-5 gap-3\">\n                {AVATARS.map((avatar) => (\n                  <button\n                    key={avatar}\n                    onClick={() => setSelectedAvatar(avatar)}\n                    className={`w-12 h-12 text-2xl rounded-lg border-2 transition-all hover:scale-110 ${\n                      selectedAvatar === avatar\n                        ? 'border-purple-500 bg-purple-100 dark:bg-purple-900'\n                        : 'border-gray-300 dark:border-gray-600 hover:border-purple-300'\n                    }`}\n                  >\n                    {avatar}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Предпросмотр */}\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center\">\n              <div className=\"text-3xl mb-2\">{selectedAvatar}</div>\n              <p className=\"font-medium text-gray-900 dark:text-white\">\n                {nickname || 'Твой никнейм'}\n              </p>\n            </div>\n\n            {/* Кнопка далее */}\n            <Button\n              onClick={handleNext}\n              disabled={nicknameStatus !== 'available' || !selectedAvatar}\n              className=\"w-full py-3 text-lg font-medium bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              Далее\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,UAAU;IACd;IAAM;IAAM;IAAM;IAAM;IAAM;IAAS;IAAS;IAAS;IAAS;IAClE;IAAS;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CAC1D;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IACjG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,kBAAkB;gBAClB;YACF;YAEA,MAAM,YAAY;oDAAW;oBAC3B,MAAM,cAAc;gBACtB;mDAAG;YAEH;0CAAO,IAAM,aAAa;;QAC5B;iCAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,OAAO;QAC3B,cAAc;QACd,IAAI;YACF,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,MAAM,CAAC,mCAAmC,EAAE,mBAAmB,mBAAmB,aAAa,CAAC,aAAa,EAAE,YAAY,GAAG,IAAI;YACxI,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,SAAS,EAAE;gBAClB,kBAAkB;gBAClB,gBAAgB;YAClB,OAAO;gBACL,kBAAkB,KAAK,KAAK,CAAC,QAAQ,CAAC,iBAAiB,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa,YAAY;gBACpG,gBAAgB,KAAK,KAAK;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,kBAAkB;YAClB,gBAAgB;QAClB,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,mBAAmB,eAAe,gBAAgB;YACpD,sDAAsD;YACtD,aAAa,OAAO,CAAC,uBAAuB;YAC5C,aAAa,OAAO,CAAC,qBAAqB;YAC1C,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,YAAY,OAAO;QACvB,IAAI,mBAAmB,aAAa,OAAO;QAC3C,IAAI,mBAAmB,WAAW,mBAAmB,WAAW,OAAO;QACvE,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,YAAY,qBAAO,6LAAC,oNAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC1C,IAAI,mBAAmB,aAAa,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,IAAI,mBAAmB,WAAW,mBAAmB,WAAW,qBAAO,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;QACpF,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAKlD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,gBAAgB;gDAClF,WAAW,CAAC,2FAA2F,EAAE,wBAAwB,iCAAiC,CAAC;gDACnK,aAAY;gDACZ,WAAW;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;oCAGJ,8BACC,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAE9D,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAM/D,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gDAEC,SAAS,IAAM,kBAAkB;gDACjC,WAAW,CAAC,sEAAsE,EAChF,mBAAmB,SACf,uDACA,gEACJ;0DAED;+CARI;;;;;;;;;;;;;;;;0CAeb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;kDACV,YAAY;;;;;;;;;;;;0CAKjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,mBAAmB,eAAe,CAAC;gCAC7C,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA3JwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}