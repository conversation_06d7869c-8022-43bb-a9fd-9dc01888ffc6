{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA7BgB", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;KAbgB;AAeT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { Plus, Upload, BookOpen, FileText, ArrowLeft, Loader2 } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Lesson {\n  id: string\n  title: string\n  level: string\n  order: number\n  description: string\n}\n\ninterface Exercise {\n  id: string\n  lesson_id: string\n  type: string\n  order: number\n  content_json: any\n  xp_reward: number\n  lessons?: {\n    id: string\n    title: string\n    level: string\n  }\n}\n\nexport default function AdminPage() {\n  const [activeTab, setActiveTab] = useState<'lessons' | 'exercises'>('lessons')\n\n  // Data state\n  const [lessons, setLessons] = useState<Lesson[]>([])\n  const [exercises, setExercises] = useState<Exercise[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n\n  // Lesson form state\n  const [lessonForm, setLessonForm] = useState({\n    title: '',\n    level: 'A1',\n    order: 1,\n    description: ''\n  })\n\n  // Exercise form state\n  const [exerciseForm, setExerciseForm] = useState({\n    lessonId: '',\n    type: 'quiz',\n    order: 1,\n    content: ''\n  })\n\n  // Loading states\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [message, setMessage] = useState('')\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData()\n  }, [])\n\n  // Auto-calculate next order number for lessons\n  useEffect(() => {\n    if (lessons.length > 0) {\n      const levelLessons = lessons.filter(l => l.level === lessonForm.level)\n      const maxOrder = levelLessons.length > 0 ? Math.max(...levelLessons.map(l => l.order)) : 0\n      setLessonForm(prev => ({ ...prev, order: maxOrder + 1 }))\n    }\n  }, [lessonForm.level, lessons])\n\n  // Auto-calculate next order number for exercises\n  useEffect(() => {\n    if (exercises.length > 0 && exerciseForm.lessonId) {\n      const lessonExercises = exercises.filter(e => e.lesson_id === exerciseForm.lessonId)\n      const maxOrder = lessonExercises.length > 0 ? Math.max(...lessonExercises.map(e => e.order)) : 0\n      setExerciseForm(prev => ({ ...prev, order: maxOrder + 1 }))\n    }\n  }, [exerciseForm.lessonId, exercises])\n\n  // JSON templates for different exercise types\n  const getExerciseTemplate = (type: string) => {\n    const templates = {\n      'quiz': {\n        question: \"What is...?\",\n        options: [\"Option A\", \"Option B\", \"Option C\", \"Option D\"],\n        correct: \"Option A\"\n      },\n      'fill-in-the-blank': {\n        sentence: \"I ___ happy.\",\n        options: [\"am\", \"is\", \"are\", \"be\"],\n        correct: \"am\"\n      },\n      'memory-match': {\n        word_pairs: [\n          { english: \"Hello\", russian: \"Привет\" },\n          { english: \"Thank you\", russian: \"Спасибо\" },\n          { english: \"Goodbye\", russian: \"До свидания\" },\n          { english: \"Please\", russian: \"Пожалуйста\" },\n          { english: \"Yes\", russian: \"Да\" },\n          { english: \"No\", russian: \"Нет\" }\n        ]\n      },\n      'sentence-builder': {\n        translation: \"Меня зовут Анна.\",\n        correct_order: [\"My\", \"name\", \"is\", \"Anna\"],\n        extra_words: [\"am\", \"called\", \"the\"]\n      },\n      'word-puzzle': {\n        target: \"Hello\",\n        words: [\"Hello\", \"Hi\", \"Hey\", \"Goodbye\"]\n      },\n      'reading': {\n        text: \"John is a student. He studies English every day. He likes reading books.\",\n        questions: [\n          {\n            question: \"What does John study?\",\n            options: [\"Math\", \"English\", \"History\", \"Science\"],\n            correct: \"English\"\n          }\n        ]\n      },\n      'audio-quiz': {\n        audio_url: \"https://example.com/audio.mp3\",\n        question: \"What did you hear?\",\n        options: [\"Option A\", \"Option B\", \"Option C\", \"Option D\"],\n        correct: \"Option A\"\n      }\n    }\n\n    return templates[type as keyof typeof templates] || templates.quiz\n  }\n\n  // Update content when exercise type changes\n  useEffect(() => {\n    const template = getExerciseTemplate(exerciseForm.type)\n    setExerciseForm(prev => ({\n      ...prev,\n      content: JSON.stringify(template, null, 2)\n    }))\n  }, [exerciseForm.type])\n\n  // Get description for exercise type\n  const getExerciseDescription = (type: string) => {\n    const descriptions = {\n      'quiz': 'Multiple choice question with 4 options',\n      'fill-in-the-blank': 'Complete sentence with correct word',\n      'memory-match': 'Match English words with Russian translations',\n      'sentence-builder': 'Arrange words to build correct sentence',\n      'word-puzzle': 'Find the correct word from options',\n      'reading': 'Read text and answer comprehension questions',\n      'audio-quiz': 'Listen to audio and answer questions'\n    }\n\n    return descriptions[type as keyof typeof descriptions] || 'Exercise description'\n  }\n\n  const loadData = async () => {\n    setIsLoading(true)\n    try {\n      const [lessonsResponse, exercisesResponse] = await Promise.all([\n        fetch('/api/lessons'),\n        fetch('/api/exercises')\n      ])\n\n      if (lessonsResponse.ok) {\n        const lessonsData = await lessonsResponse.json()\n        setLessons(lessonsData.lessons || [])\n      }\n\n      if (exercisesResponse.ok) {\n        const exercisesData = await exercisesResponse.json()\n        setExercises(exercisesData.exercises || [])\n      }\n    } catch (error) {\n      console.error('Error loading data:', error)\n      setMessage('❌ Error loading data')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Handle lesson form submission\n  const handleLessonSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    setMessage('')\n\n    try {\n      const response = await fetch('/api/lessons', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(lessonForm),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        setMessage('✅ Lesson created successfully!')\n        setLessonForm({ title: '', level: 'A1', order: 1, description: '' })\n        // Reload lessons to update the dropdown\n        await loadData()\n      } else {\n        setMessage(`❌ ${data.error || 'Error creating lesson'}`)\n      }\n    } catch (error) {\n      setMessage('❌ Error creating lesson. Please try again.')\n      console.error('Error:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  // Handle exercise form submission\n  const handleExerciseSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    setMessage('')\n\n    try {\n      // Validate JSON content\n      JSON.parse(exerciseForm.content)\n\n      const response = await fetch('/api/exercises', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(exerciseForm),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        setMessage('✅ Exercise created successfully!')\n        setExerciseForm({ lessonId: '', type: 'quiz', order: 1, content: '' })\n        // Reload exercises to update the list\n        await loadData()\n      } else {\n        setMessage(`❌ ${data.error || 'Error creating exercise'}`)\n      }\n    } catch (error) {\n      if (error instanceof SyntaxError) {\n        setMessage('❌ Invalid JSON format. Please check your content.')\n      } else {\n        setMessage('❌ Error creating exercise. Please try again.')\n      }\n      console.error('Error:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Loading admin panel...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"flex items-center gap-4 mb-2\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n            </Link>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Admin Panel</h1>\n              <p className=\"text-gray-600\">Manage lessons and exercises</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-6\">\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('lessons')}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'lessons'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <BookOpen className=\"h-4 w-4\" />\n            <span>Lessons</span>\n          </button>\n          <button\n            onClick={() => setActiveTab('exercises')}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'exercises'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FileText className=\"h-4 w-4\" />\n            <span>Exercises</span>\n          </button>\n        </div>\n\n        {/* Lessons Tab */}\n        {activeTab === 'lessons' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Create New Lesson</h2>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleLessonSubmit}>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Title\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={lessonForm.title}\n                        onChange={(e) => setLessonForm({...lessonForm, title: e.target.value})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Enter lesson title\"\n                        required\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Level\n                      </label>\n                      <select\n                        value={lessonForm.level}\n                        onChange={(e) => setLessonForm({...lessonForm, level: e.target.value})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"A1\">A1</option>\n                        <option value=\"A2\">A2</option>\n                        <option value=\"B1\">B1</option>\n                        <option value=\"B2\">B2</option>\n                        <option value=\"C1\">C1</option>\n                        <option value=\"C2\">C2</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Order\n                      </label>\n                      <input\n                        type=\"number\"\n                        min=\"1\"\n                        value={lessonForm.order}\n                        onChange={(e) => setLessonForm({...lessonForm, order: parseInt(e.target.value) || 1})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"1\"\n                        required\n                      />\n                    </div>\n                    <div className=\"md:col-span-3\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Description\n                      </label>\n                      <textarea\n                        rows={3}\n                        value={lessonForm.description}\n                        onChange={(e) => setLessonForm({...lessonForm, description: e.target.value})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Enter lesson description\"\n                        required\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"mt-6 flex items-center justify-between\">\n                    <Button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"flex items-center\"\n                    >\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      {isSubmitting ? 'Adding...' : 'Add Lesson'}\n                    </Button>\n\n                    {message && (\n                      <div className={`text-sm ${message.includes('✅') ? 'text-green-600' : 'text-red-600'}`}>\n                        {message}\n                      </div>\n                    )}\n                  </div>\n                </form>\n              </CardContent>\n            </Card>\n\n            {/* Existing Lessons */}\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Existing Lessons ({lessons.length})</h2>\n              </CardHeader>\n              <CardContent>\n                {lessons.length === 0 ? (\n                  <p className=\"text-gray-500 text-center py-4\">No lessons created yet</p>\n                ) : (\n                  <div className=\"space-y-3\">\n                    {lessons.map((lesson) => (\n                      <div key={lesson.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <h3 className=\"font-medium text-gray-900\">{lesson.title}</h3>\n                          <p className=\"text-sm text-gray-600\">\n                            Level: {lesson.level} | Order: {lesson.order}\n                          </p>\n                          <p className=\"text-xs text-gray-500 mt-1\">{lesson.description}</p>\n                        </div>\n                        <div className=\"text-xs text-gray-400\">\n                          ID: {lesson.id.slice(0, 8)}...\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Bulk Upload</h2>\n              </CardHeader>\n              <CardContent>\n                <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                  <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-600 mb-2\">Upload JSON file with lessons</p>\n                  <p className=\"text-sm text-gray-500 mb-4\">\n                    Drag and drop or click to select\n                  </p>\n                  <Button variant=\"outline\">\n                    Choose File\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Exercises Tab */}\n        {activeTab === 'exercises' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Create New Exercise</h2>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleExerciseSubmit}>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Lesson\n                      </label>\n                      <select\n                        value={exerciseForm.lessonId}\n                        onChange={(e) => setExerciseForm({...exerciseForm, lessonId: e.target.value})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        required\n                        disabled={isLoading}\n                      >\n                        <option value=\"\">\n                          {isLoading ? 'Loading lessons...' : 'Select a lesson'}\n                        </option>\n                        {lessons.map((lesson) => (\n                          <option key={lesson.id} value={lesson.id}>\n                            {lesson.level} - {lesson.title} (Order: {lesson.order})\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Exercise Type\n                      </label>\n                      <select\n                        value={exerciseForm.type}\n                        onChange={(e) => setExerciseForm({...exerciseForm, type: e.target.value})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"quiz\">Quiz</option>\n                        <option value=\"fill-in-the-blank\">Fill in the Blank</option>\n                        <option value=\"word-puzzle\">Word Puzzle</option>\n                        <option value=\"sentence-builder\">Sentence Builder</option>\n                        <option value=\"reading\">Reading</option>\n                        <option value=\"memory-match\">Memory Match</option>\n                        <option value=\"audio-quiz\">Audio Quiz</option>\n                      </select>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {getExerciseDescription(exerciseForm.type)}\n                      </p>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Order\n                      </label>\n                      <input\n                        type=\"number\"\n                        min=\"1\"\n                        value={exerciseForm.order}\n                        onChange={(e) => setExerciseForm({...exerciseForm, order: parseInt(e.target.value) || 1})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"1\"\n                        required\n                      />\n                    </div>\n                    <div className=\"md:col-span-3\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Exercise Content (JSON) - {exerciseForm.type.charAt(0).toUpperCase() + exerciseForm.type.slice(1).replace('-', ' ')}\n                      </label>\n                      <div className=\"mb-2\">\n                        <p className=\"text-xs text-gray-500\">\n                          Template loaded automatically. Edit the values as needed:\n                        </p>\n                      </div>\n                      <textarea\n                        rows={12}\n                        value={exerciseForm.content}\n                        onChange={(e) => setExerciseForm({...exerciseForm, content: e.target.value})}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm leading-relaxed\"\n                        placeholder=\"JSON template will be loaded automatically...\"\n                        required\n                      />\n                      <div className=\"mt-2 flex items-center gap-2\">\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            const template = getExerciseTemplate(exerciseForm.type)\n                            setExerciseForm(prev => ({\n                              ...prev,\n                              content: JSON.stringify(template, null, 2)\n                            }))\n                          }}\n                        >\n                          Reset Template\n                        </Button>\n                        <span className=\"text-xs text-gray-500\">\n                          Click to restore original template\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-6 flex items-center justify-between\">\n                    <Button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"flex items-center\"\n                    >\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      {isSubmitting ? 'Adding...' : 'Add Exercise'}\n                    </Button>\n\n                    {message && (\n                      <div className={`text-sm ${message.includes('✅') ? 'text-green-600' : 'text-red-600'}`}>\n                        {message}\n                      </div>\n                    )}\n                  </div>\n                </form>\n              </CardContent>\n            </Card>\n\n            {/* Existing Exercises */}\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Existing Exercises ({exercises.length})</h2>\n              </CardHeader>\n              <CardContent>\n                {exercises.length === 0 ? (\n                  <p className=\"text-gray-500 text-center py-4\">No exercises created yet</p>\n                ) : (\n                  <div className=\"space-y-3\">\n                    {exercises.map((exercise) => (\n                      <div key={exercise.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-2 mb-1\">\n                            <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded\">\n                              {exercise.type}\n                            </span>\n                            <span className=\"text-sm font-medium text-gray-900\">\n                              Order: {exercise.order}\n                            </span>\n                          </div>\n                          <p className=\"text-sm text-gray-600\">\n                            Lesson: {exercise.lessons?.title || 'Unknown'} ({exercise.lessons?.level})\n                          </p>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            XP Reward: {exercise.xp_reward}\n                          </p>\n                        </div>\n                        <div className=\"text-xs text-gray-400\">\n                          ID: {exercise.id.slice(0, 8)}...\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Exercise Templates</h2>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"p-4 border border-gray-200 rounded-lg\">\n                    <h3 className=\"font-medium mb-2\">Quiz Template</h3>\n                    <pre className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto\">\n{`{\n  \"question\": \"What is...?\",\n  \"options\": [\"A\", \"B\", \"C\", \"D\"],\n  \"correct\": \"A\"\n}`}\n                    </pre>\n                  </div>\n                  <div className=\"p-4 border border-gray-200 rounded-lg\">\n                    <h3 className=\"font-medium mb-2\">Fill-in-the-Blank</h3>\n                    <pre className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto\">\n{`{\n  \"sentence\": \"I ___ happy.\",\n  \"options\": [\"am\", \"is\", \"are\"],\n  \"correct\": \"am\"\n}`}\n                    </pre>\n                  </div>\n                  <div className=\"p-4 border border-gray-200 rounded-lg\">\n                    <h3 className=\"font-medium mb-2\">Memory Match</h3>\n                    <pre className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto\">\n{`{\n  \"word_pairs\": [\n    {\"english\": \"Hello\", \"russian\": \"Привет\"},\n    {\"english\": \"Thank you\", \"russian\": \"Спасибо\"},\n    {\"english\": \"Goodbye\", \"russian\": \"До свидания\"}\n  ]\n}`}\n                    </pre>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AA8Be,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAEpE,aAAa;IACb,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oBAAoB;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;IAEA,sBAAsB;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,UAAU;QACV,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,iBAAiB;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,MAAM,eAAe,QAAQ,MAAM;wDAAC,CAAA,IAAK,EAAE,KAAK,KAAK,WAAW,KAAK;;gBACrE,MAAM,WAAW,aAAa,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,aAAa,GAAG;2CAAC,CAAA,IAAK,EAAE,KAAK;6CAAK;gBACzF;2CAAc,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO,WAAW;wBAAE,CAAC;;YACzD;QACF;8BAAG;QAAC,WAAW,KAAK;QAAE;KAAQ;IAE9B,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,UAAU,MAAM,GAAG,KAAK,aAAa,QAAQ,EAAE;gBACjD,MAAM,kBAAkB,UAAU,MAAM;2DAAC,CAAA,IAAK,EAAE,SAAS,KAAK,aAAa,QAAQ;;gBACnF,MAAM,WAAW,gBAAgB,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,gBAAgB,GAAG;2CAAC,CAAA,IAAK,EAAE,KAAK;6CAAK;gBAC/F;2CAAgB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO,WAAW;wBAAE,CAAC;;YAC3D;QACF;8BAAG;QAAC,aAAa,QAAQ;QAAE;KAAU;IAErC,8CAA8C;IAC9C,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY;YAChB,QAAQ;gBACN,UAAU;gBACV,SAAS;oBAAC;oBAAY;oBAAY;oBAAY;iBAAW;gBACzD,SAAS;YACX;YACA,qBAAqB;gBACnB,UAAU;gBACV,SAAS;oBAAC;oBAAM;oBAAM;oBAAO;iBAAK;gBAClC,SAAS;YACX;YACA,gBAAgB;gBACd,YAAY;oBACV;wBAAE,SAAS;wBAAS,SAAS;oBAAS;oBACtC;wBAAE,SAAS;wBAAa,SAAS;oBAAU;oBAC3C;wBAAE,SAAS;wBAAW,SAAS;oBAAc;oBAC7C;wBAAE,SAAS;wBAAU,SAAS;oBAAa;oBAC3C;wBAAE,SAAS;wBAAO,SAAS;oBAAK;oBAChC;wBAAE,SAAS;wBAAM,SAAS;oBAAM;iBACjC;YACH;YACA,oBAAoB;gBAClB,aAAa;gBACb,eAAe;oBAAC;oBAAM;oBAAQ;oBAAM;iBAAO;gBAC3C,aAAa;oBAAC;oBAAM;oBAAU;iBAAM;YACtC;YACA,eAAe;gBACb,QAAQ;gBACR,OAAO;oBAAC;oBAAS;oBAAM;oBAAO;iBAAU;YAC1C;YACA,WAAW;gBACT,MAAM;gBACN,WAAW;oBACT;wBACE,UAAU;wBACV,SAAS;4BAAC;4BAAQ;4BAAW;4BAAW;yBAAU;wBAClD,SAAS;oBACX;iBACD;YACH;YACA,cAAc;gBACZ,WAAW;gBACX,UAAU;gBACV,SAAS;oBAAC;oBAAY;oBAAY;oBAAY;iBAAW;gBACzD,SAAS;YACX;QACF;QAEA,OAAO,SAAS,CAAC,KAA+B,IAAI,UAAU,IAAI;IACpE;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,WAAW,oBAAoB,aAAa,IAAI;YACtD;uCAAgB,CAAA,OAAQ,CAAC;wBACvB,GAAG,IAAI;wBACP,SAAS,KAAK,SAAS,CAAC,UAAU,MAAM;oBAC1C,CAAC;;QACH;8BAAG;QAAC,aAAa,IAAI;KAAC;IAEtB,oCAAoC;IACpC,MAAM,yBAAyB,CAAC;QAC9B,MAAM,eAAe;YACnB,QAAQ;YACR,qBAAqB;YACrB,gBAAgB;YAChB,oBAAoB;YACpB,eAAe;YACf,WAAW;YACX,cAAc;QAChB;QAEA,OAAO,YAAY,CAAC,KAAkC,IAAI;IAC5D;IAEA,MAAM,WAAW;QACf,aAAa;QACb,IAAI;YACF,MAAM,CAAC,iBAAiB,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,MAAM;gBACN,MAAM;aACP;YAED,IAAI,gBAAgB,EAAE,EAAE;gBACtB,MAAM,cAAc,MAAM,gBAAgB,IAAI;gBAC9C,WAAW,YAAY,OAAO,IAAI,EAAE;YACtC;YAEA,IAAI,kBAAkB,EAAE,EAAE;gBACxB,MAAM,gBAAgB,MAAM,kBAAkB,IAAI;gBAClD,aAAa,cAAc,SAAS,IAAI,EAAE;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF;IAEA,gCAAgC;IAChC,MAAM,qBAAqB,OAAO;QAChC,EAAE,cAAc;QAChB,gBAAgB;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,cAAc;oBAAE,OAAO;oBAAI,OAAO;oBAAM,OAAO;oBAAG,aAAa;gBAAG;gBAClE,wCAAwC;gBACxC,MAAM;YACR,OAAO;gBACL,WAAW,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,yBAAyB;YACzD;QACF,EAAE,OAAO,OAAO;YACd,WAAW;YACX,QAAQ,KAAK,CAAC,UAAU;QAC1B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,kCAAkC;IAClC,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,gBAAgB;QAChB,WAAW;QAEX,IAAI;YACF,wBAAwB;YACxB,KAAK,KAAK,CAAC,aAAa,OAAO;YAE/B,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,gBAAgB;oBAAE,UAAU;oBAAI,MAAM;oBAAQ,OAAO;oBAAG,SAAS;gBAAG;gBACpE,sCAAsC;gBACtC,MAAM;YACR,OAAO;gBACL,WAAW,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,2BAA2B;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,aAAa;gBAChC,WAAW;YACb,OAAO;gBACL,WAAW;YACb;YACA,QAAQ,KAAK,CAAC,UAAU;QAC1B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6GAA6G,EACvH,cAAc,YACV,qCACA,qCACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6GAA6G,EACvH,cAAc,cACV,qCACA,qCACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAKT,cAAc,2BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;kDAExC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAK,UAAU;;8DACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,WAAW,KAAK;oEACvB,UAAU,CAAC,IAAM,cAAc;4EAAC,GAAG,UAAU;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAA;oEACpE,WAAU;oEACV,aAAY;oEACZ,QAAQ;;;;;;;;;;;;sEAGZ,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,OAAO,WAAW,KAAK;oEACvB,UAAU,CAAC,IAAM,cAAc;4EAAC,GAAG,UAAU;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAA;oEACpE,WAAU;;sFAEV,6LAAC;4EAAO,OAAM;sFAAK;;;;;;sFACnB,6LAAC;4EAAO,OAAM;sFAAK;;;;;;sFACnB,6LAAC;4EAAO,OAAM;sFAAK;;;;;;sFACnB,6LAAC;4EAAO,OAAM;sFAAK;;;;;;sFACnB,6LAAC;4EAAO,OAAM;sFAAK;;;;;;sFACnB,6LAAC;4EAAO,OAAM;sFAAK;;;;;;;;;;;;;;;;;;sEAGvB,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,KAAI;oEACJ,OAAO,WAAW,KAAK;oEACvB,UAAU,CAAC,IAAM,cAAc;4EAAC,GAAG,UAAU;4EAAE,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEAAC;oEACnF,WAAU;oEACV,aAAY;oEACZ,QAAQ;;;;;;;;;;;;sEAGZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAM;oEACN,OAAO,WAAW,WAAW;oEAC7B,UAAU,CAAC,IAAM,cAAc;4EAAC,GAAG,UAAU;4EAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wEAAA;oEAC1E,WAAU;oEACV,aAAY;oEACZ,QAAQ;;;;;;;;;;;;;;;;;;8DAKd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,UAAU;4DACV,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,eAAe,cAAc;;;;;;;wDAG/B,yBACC,6LAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,OAAO,mBAAmB,gBAAgB;sEACnF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;;gDAAwB;gDAAmB,QAAQ,MAAM;gDAAC;;;;;;;;;;;;kDAE1E,6LAAC,mIAAA,CAAA,cAAW;kDACT,QAAQ,MAAM,KAAK,kBAClB,6LAAC;4CAAE,WAAU;sDAAiC;;;;;iEAE9C,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAAoB,WAAU;;sEAC7B,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA6B,OAAO,KAAK;;;;;;8EACvD,6LAAC;oEAAE,WAAU;;wEAAwB;wEAC3B,OAAO,KAAK;wEAAC;wEAAW,OAAO,KAAK;;;;;;;8EAE9C,6LAAC;oEAAE,WAAU;8EAA8B,OAAO,WAAW;;;;;;;;;;;;sEAE/D,6LAAC;4DAAI,WAAU;;gEAAwB;gEAChC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG;gEAAG;;;;;;;;mDATrB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;0CAkB7B,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;kDAExC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUnC,cAAc,6BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;kDAExC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAK,UAAU;;8DACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,OAAO,aAAa,QAAQ;oEAC5B,UAAU,CAAC,IAAM,gBAAgB;4EAAC,GAAG,YAAY;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAA;oEAC3E,WAAU;oEACV,QAAQ;oEACR,UAAU;;sFAEV,6LAAC;4EAAO,OAAM;sFACX,YAAY,uBAAuB;;;;;;wEAErC,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gFAAuB,OAAO,OAAO,EAAE;;oFACrC,OAAO,KAAK;oFAAC;oFAAI,OAAO,KAAK;oFAAC;oFAAU,OAAO,KAAK;oFAAC;;+EAD3C,OAAO,EAAE;;;;;;;;;;;;;;;;;sEAM5B,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,OAAO,aAAa,IAAI;oEACxB,UAAU,CAAC,IAAM,gBAAgB;4EAAC,GAAG,YAAY;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAA;oEACvE,WAAU;;sFAEV,6LAAC;4EAAO,OAAM;sFAAO;;;;;;sFACrB,6LAAC;4EAAO,OAAM;sFAAoB;;;;;;sFAClC,6LAAC;4EAAO,OAAM;sFAAc;;;;;;sFAC5B,6LAAC;4EAAO,OAAM;sFAAmB;;;;;;sFACjC,6LAAC;4EAAO,OAAM;sFAAU;;;;;;sFACxB,6LAAC;4EAAO,OAAM;sFAAe;;;;;;sFAC7B,6LAAC;4EAAO,OAAM;sFAAa;;;;;;;;;;;;8EAE7B,6LAAC;oEAAE,WAAU;8EACV,uBAAuB,aAAa,IAAI;;;;;;;;;;;;sEAG7C,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,KAAI;oEACJ,OAAO,aAAa,KAAK;oEACzB,UAAU,CAAC,IAAM,gBAAgB;4EAAC,GAAG,YAAY;4EAAE,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEAAC;oEACvF,WAAU;oEACV,aAAY;oEACZ,QAAQ;;;;;;;;;;;;sEAGZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;;wEAA+C;wEACnC,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;8EAEjH,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;8EAIvC,6LAAC;oEACC,MAAM;oEACN,OAAO,aAAa,OAAO;oEAC3B,UAAU,CAAC,IAAM,gBAAgB;4EAAC,GAAG,YAAY;4EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAA;oEAC1E,WAAU;oEACV,aAAY;oEACZ,QAAQ;;;;;;8EAEV,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS;gFACP,MAAM,WAAW,oBAAoB,aAAa,IAAI;gFACtD,gBAAgB,CAAA,OAAQ,CAAC;wFACvB,GAAG,IAAI;wFACP,SAAS,KAAK,SAAS,CAAC,UAAU,MAAM;oFAC1C,CAAC;4EACH;sFACD;;;;;;sFAGD,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;8DAO9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,UAAU;4DACV,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,eAAe,cAAc;;;;;;;wDAG/B,yBACC,6LAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,OAAO,mBAAmB,gBAAgB;sEACnF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;;gDAAwB;gDAAqB,UAAU,MAAM;gDAAC;;;;;;;;;;;;kDAE9E,6LAAC,mIAAA,CAAA,cAAW;kDACT,UAAU,MAAM,KAAK,kBACpB,6LAAC;4CAAE,WAAU;sDAAiC;;;;;iEAE9C,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFACb,SAAS,IAAI;;;;;;sFAEhB,6LAAC;4EAAK,WAAU;;gFAAoC;gFAC1C,SAAS,KAAK;;;;;;;;;;;;;8EAG1B,6LAAC;oEAAE,WAAU;;wEAAwB;wEAC1B,SAAS,OAAO,EAAE,SAAS;wEAAU;wEAAG,SAAS,OAAO,EAAE;wEAAM;;;;;;;8EAE3E,6LAAC;oEAAE,WAAU;;wEAA6B;wEAC5B,SAAS,SAAS;;;;;;;;;;;;;sEAGlC,6LAAC;4DAAI,WAAU;;gEAAwB;gEAChC,SAAS,EAAE,CAAC,KAAK,CAAC,GAAG;gEAAG;;;;;;;;mDAlBvB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;0CA2B/B,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;kDAExC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEAClC,CAAC;;;;CAID,CAAC;;;;;;;;;;;;8DAGgB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEAClC,CAAC;;;;CAID,CAAC;;;;;;;;;;;;8DAGgB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEAClC,CAAC;;;;;;CAMD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWF;GAxnBwB;KAAA", "debugId": null}}]}