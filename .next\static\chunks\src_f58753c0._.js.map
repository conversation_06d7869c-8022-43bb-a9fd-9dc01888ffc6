{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA7BgB", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;KAbgB;AAeT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { Plus, Upload, BookOpen, FileText, ArrowLeft } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function AdminPage() {\n  const [activeTab, setActiveTab] = useState<'lessons' | 'exercises'>('lessons')\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"flex items-center gap-4 mb-2\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n            </Link>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Admin Panel</h1>\n              <p className=\"text-gray-600\">Manage lessons and exercises</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-6\">\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('lessons')}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'lessons'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <BookOpen className=\"h-4 w-4\" />\n            <span>Lessons</span>\n          </button>\n          <button\n            onClick={() => setActiveTab('exercises')}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'exercises'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FileText className=\"h-4 w-4\" />\n            <span>Exercises</span>\n          </button>\n        </div>\n\n        {/* Lessons Tab */}\n        {activeTab === 'lessons' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <h2 className=\"text-lg font-semibold\">Create New Lesson</h2>\n                  <Button size=\"sm\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Lesson\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Title\n                    </label>\n                    <input\n                      type=\"text\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"Enter lesson title\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Level\n                    </label>\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                      <option value=\"A1\">A1</option>\n                      <option value=\"A2\">A2</option>\n                      <option value=\"B1\">B1</option>\n                      <option value=\"B2\">B2</option>\n                      <option value=\"C1\">C1</option>\n                      <option value=\"C2\">C2</option>\n                    </select>\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Description\n                    </label>\n                    <textarea\n                      rows={3}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"Enter lesson description\"\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Bulk Upload</h2>\n              </CardHeader>\n              <CardContent>\n                <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                  <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-600 mb-2\">Upload JSON file with lessons</p>\n                  <p className=\"text-sm text-gray-500 mb-4\">\n                    Drag and drop or click to select\n                  </p>\n                  <Button variant=\"outline\">\n                    Choose File\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Exercises Tab */}\n        {activeTab === 'exercises' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <h2 className=\"text-lg font-semibold\">Create New Exercise</h2>\n                  <Button size=\"sm\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Exercise\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Lesson\n                    </label>\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                      <option value=\"\">Select a lesson</option>\n                      <option value=\"lesson-1\">Basic Greetings</option>\n                      <option value=\"lesson-2\">Numbers 1-10</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Exercise Type\n                    </label>\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                      <option value=\"quiz\">Quiz</option>\n                      <option value=\"fill-in-the-blank\">Fill in the Blank</option>\n                      <option value=\"word-puzzle\">Word Puzzle</option>\n                      <option value=\"sentence-builder\">Sentence Builder</option>\n                      <option value=\"reading\">Reading</option>\n                      <option value=\"memory-match\">Memory Match</option>\n                      <option value=\"audio-quiz\">Audio Quiz</option>\n                    </select>\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Exercise Content (JSON)\n                    </label>\n                    <textarea\n                      rows={8}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm\"\n                      placeholder='{\"question\": \"What is...?\", \"options\": [...], \"correct\": \"...\"}'\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-lg font-semibold\">Exercise Templates</h2>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"p-4 border border-gray-200 rounded-lg\">\n                    <h3 className=\"font-medium mb-2\">Quiz Template</h3>\n                    <pre className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto\">\n{`{\n  \"question\": \"What is...?\",\n  \"options\": [\"A\", \"B\", \"C\", \"D\"],\n  \"correct\": \"A\"\n}`}\n                    </pre>\n                  </div>\n                  <div className=\"p-4 border border-gray-200 rounded-lg\">\n                    <h3 className=\"font-medium mb-2\">Fill-in-the-Blank</h3>\n                    <pre className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto\">\n{`{\n  \"sentence\": \"I ___ happy.\",\n  \"options\": [\"am\", \"is\", \"are\"],\n  \"correct\": \"am\"\n}`}\n                    </pre>\n                  </div>\n                  <div className=\"p-4 border border-gray-200 rounded-lg\">\n                    <h3 className=\"font-medium mb-2\">Memory Match</h3>\n                    <pre className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto\">\n{`{\n  \"word_pairs\": [\n    {\"english\": \"Hello\", \"russian\": \"Привет\"},\n    {\"english\": \"Thank you\", \"russian\": \"Спасибо\"},\n    {\"english\": \"Goodbye\", \"russian\": \"До свидания\"}\n  ]\n}`}\n                    </pre>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAEpE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6GAA6G,EACvH,cAAc,YACV,qCACA,qCACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,6GAA6G,EACvH,cAAc,cACV,qCACA,qCACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAKT,cAAc,2BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;;sEACX,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;kDAKvC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC;oEAAO,OAAM;8EAAK;;;;;;8EACnB,6LAAC;oEAAO,OAAM;8EAAK;;;;;;8EACnB,6LAAC;oEAAO,OAAM;8EAAK;;;;;;8EACnB,6LAAC;oEAAO,OAAM;8EAAK;;;;;;8EACnB,6LAAC;oEAAO,OAAM;8EAAK;;;;;;8EACnB,6LAAC;oEAAO,OAAM;8EAAK;;;;;;;;;;;;;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;kDAExC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUnC,cAAc,6BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;;sEACX,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;kDAKvC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,6LAAC;oEAAO,OAAM;8EAAW;;;;;;8EACzB,6LAAC;oEAAO,OAAM;8EAAW;;;;;;;;;;;;;;;;;;8DAG7B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,6LAAC;oEAAO,OAAM;8EAAoB;;;;;;8EAClC,6LAAC;oEAAO,OAAM;8EAAc;;;;;;8EAC5B,6LAAC;oEAAO,OAAM;8EAAmB;;;;;;8EACjC,6LAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,6LAAC;oEAAO,OAAM;8EAAe;;;;;;8EAC7B,6LAAC;oEAAO,OAAM;8EAAa;;;;;;;;;;;;;;;;;;8DAG/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;kDAExC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEAClC,CAAC;;;;CAID,CAAC;;;;;;;;;;;;8DAGgB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEAClC,CAAC;;;;CAID,CAAC;;;;;;;;;;;;8DAGgB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEAClC,CAAC;;;;;;CAMD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWF;GA9NwB;KAAA", "debugId": null}}]}