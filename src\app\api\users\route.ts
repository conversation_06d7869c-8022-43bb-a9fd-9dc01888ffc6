import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// GET - получить пользователя по telegram_id
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const telegramId = searchParams.get('telegram_id')

    if (!telegramId) {
      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })
    }

    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ user: user || null })
  } catch (error) {
    console.error('Error in GET /api/users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - создать нового пользователя
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { telegramId, nickname, avatar, level, theme = 'light' } = body

    // Валидация
    if (!telegramId || !nickname || !avatar || !level) {
      return NextResponse.json(
        { error: 'All fields are required: telegramId, nickname, avatar, level' },
        { status: 400 }
      )
    }

    // Проверяем, не занят ли никнейм
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('nickname', nickname)
      .single()

    if (existingUser) {
      return NextResponse.json(
        { error: 'Nickname is already taken' },
        { status: 409 }
      )
    }

    // Создаем пользователя
    const { data: user, error } = await supabase
      .from('users')
      .insert([{
        telegram_id: telegramId,
        nickname,
        avatar,
        level,
        theme,
        is_onboarded: true,
        total_xp: 0,
        current_streak: 0,
        last_activity_date: new Date().toISOString().split('T')[0]
      }])
      .select()
      .single()

    if (error) {
      console.error('Error creating user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Настройки пользователя будут добавлены позже при необходимости

    return NextResponse.json({ user }, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - обновить пользователя
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { telegramId, ...updateData } = body

    if (!telegramId) {
      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })
    }

    const { data: user, error } = await supabase
      .from('users')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', telegramId)
      .select()
      .single()

    if (error) {
      console.error('Error updating user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ user })
  } catch (error) {
    console.error('Error in PUT /api/users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
