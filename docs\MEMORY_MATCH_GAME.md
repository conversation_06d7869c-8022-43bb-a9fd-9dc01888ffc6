# Memory Match Game - Игра на сопоставление слов

## Описание

Memory Match Game - это новая интерактивная игра для изучения английского языка, где пользователи должны найти пары соответствующих английских и русских слов в ограниченное время.

## Особенности игры

### 🎯 Игровая механика
- **Игровое поле**: 2 колонки карточек (6 карточек в каждой)
- **Карточки**: Половина с английскими словами, половина с русскими переводами
- **Цель**: Найти все пары слов до окончания времени

### 🎯 Система очков
- Начальный счёт: 100 очков
- Штраф за ошибку: -10 очков
- Отображение количества ошибок

### 🎨 Визуальная обратная связь
- **Выбранная карточка**: Светло-серый фон
- **Правильная пара**: Зеленый фон на 0.5 сек, затем карточки полностью исчезают с поля
- **Неправильная пара**: Красный фон на 1 сек, карточки остаются

### 🎮 Интерактивность
- Тактильная обратная связь через Telegram WebApp
- Звуковые сигналы (легкая вибрация для правильных ответов, тяжелая для неправильных)
- Кнопка сброса игры

## Техническая реализация

### Компонент: `MemoryMatchGame`

```typescript
interface MemoryMatchGameProps {
  wordPairs: Array<{ english: string; russian: string }>
  onComplete: (correct: boolean, score?: number) => void
}
```

### Структура данных в базе

```json
{
  "type": "memory-match",
  "content_json": {
    "word_pairs": [
      { "english": "Hello", "russian": "Привет" },
      { "english": "Thank you", "russian": "Спасибо" }
    ]
  },
  "xp_reward": 20
}
```

### Состояния игры

1. **Начальное состояние**: Показ инструкций и кнопки "Начать игру"
2. **Игровой процесс**: Активное игровое поле со счетчиком очков и ошибок
3. **Завершение**: Показ результатов и итогового счета

## Система подсчета очков

- **Начальный счет**: 100 очков
- **Штраф за ошибку**: -10 очков за каждую неправильную пару
- **Минимальный счет**: 0 очков
- **Отображение**: Текущий счет и количество ошибок в реальном времени

## Интеграция с системой

### База данных
- Новый тип упражнения `memory-match` добавлен в CHECK constraint
- Миграция: `002_add_memory_match_exercise.sql`

### Компоненты
- `src/components/exercises/MemoryMatchGame.tsx` - основной компонент игры
- Интеграция в `src/app/lesson/[id]/page.tsx`

### Тестирование
- Тестовая страница: `/test-memory-match`
- Mock данные в `src/lib/database.ts`

## Использование

### В уроке
Игра автоматически загружается как часть урока, если упражнение имеет тип `memory-match`.

### Тестирование
Перейдите на `/test-memory-match` для тестирования игры с примерными данными.

### Добавление новых упражнений
Используйте скрипт `scripts/add-memory-match-exercises.js` для массового добавления упражнений memory-match во все уроки.

## Будущие улучшения

- [ ] Добавление звуковых эффектов
- [ ] Анимации переворота карточек
- [ ] Различные размеры игрового поля (4x4, 6x6)
- [ ] Режим без ограничения времени
- [ ] Статистика ошибок и времени реакции
