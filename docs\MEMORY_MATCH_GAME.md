# Memory Match Game - Игра на сопоставление слов

## Описание

Memory Match Game - это новая интерактивная игра для изучения английского языка, где пользователи должны найти пары соответствующих английских и русских слов в ограниченное время.

## Особенности игры

### 🎯 Игровая механика
- **Игровое поле**: 2 колонки карточек (6 карточек в каждой)
- **Карточки**: Половина с английскими словами, половина с русскими переводами
- **Цель**: Найти все пары слов до окончания времени

### ⏱️ Таймер
- Настраиваемое время игры (по умолчанию 120 секунд)
- Время зависит от уровня сложности:
  - A1: 150 секунд
  - A2: 140 секунд  
  - B1: 130 секунд
  - B2: 120 секунд
  - C1: 110 секунд
  - C2: 100 секунд

### 🎨 Визуальная обратная связь
- **Выбранная карточка**: Светло-серый фон
- **Правильная пара**: Зеленый фон, карточки исчезают
- **Неправильная пара**: Красный фон, карточки остаются

### 🎮 Интерактивность
- Тактильная обратная связь через Telegram WebApp
- Звуковые сигналы (легкая вибрация для правильных ответов, тяжелая для неправильных)
- Кнопка сброса игры

## Техническая реализация

### Компонент: `MemoryMatchGame`

```typescript
interface MemoryMatchGameProps {
  wordPairs: Array<{ english: string; russian: string }>
  timeLimit?: number // в секундах
  onComplete: (correct: boolean, score?: number) => void
}
```

### Структура данных в базе

```json
{
  "type": "memory-match",
  "content_json": {
    "word_pairs": [
      { "english": "Hello", "russian": "Привет" },
      { "english": "Thank you", "russian": "Спасибо" }
    ],
    "time_limit": 120
  },
  "xp_reward": 20
}
```

### Состояния игры

1. **Начальное состояние**: Показ инструкций и кнопки "Начать игру"
2. **Игровой процесс**: Активное игровое поле с таймером
3. **Завершение**: Показ результатов (успех/неудача) и счета

## Система подсчета очков

- **Базовый счет**: 100 очков за полное прохождение
- **Штраф за время**: -2 очка за каждые 2 секунды сверх минимального времени
- **Минимальный счет**: 0 очков

Формула: `Math.max(0, 100 - Math.floor((timeLimit - timeLeft) / 2))`

## Интеграция с системой

### База данных
- Новый тип упражнения `memory-match` добавлен в CHECK constraint
- Миграция: `002_add_memory_match_exercise.sql`

### Компоненты
- `src/components/exercises/MemoryMatchGame.tsx` - основной компонент игры
- Интеграция в `src/app/lesson/[id]/page.tsx`

### Тестирование
- Тестовая страница: `/test-memory-match`
- Mock данные в `src/lib/database.ts`

## Использование

### В уроке
Игра автоматически загружается как часть урока, если упражнение имеет тип `memory-match`.

### Тестирование
Перейдите на `/test-memory-match` для тестирования игры с примерными данными.

### Добавление новых упражнений
Используйте скрипт `scripts/add-memory-match-exercises.js` для массового добавления упражнений memory-match во все уроки.

## Будущие улучшения

- [ ] Добавление звуковых эффектов
- [ ] Анимации переворота карточек
- [ ] Различные размеры игрового поля (4x4, 6x6)
- [ ] Режим без ограничения времени
- [ ] Статистика ошибок и времени реакции
