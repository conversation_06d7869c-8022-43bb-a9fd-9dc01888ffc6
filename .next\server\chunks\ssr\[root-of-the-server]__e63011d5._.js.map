{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/hooks/useTelegramWebApp.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface TelegramUser {\n  id: number\n  first_name: string\n  last_name?: string\n  username?: string\n  language_code?: string\n  is_premium?: boolean\n}\n\ninterface TelegramWebApp {\n  initData: string\n  initDataUnsafe: {\n    user?: TelegramUser\n    chat_instance?: string\n    chat_type?: string\n    start_param?: string\n  }\n  version: string\n  platform: string\n  colorScheme: 'light' | 'dark'\n  themeParams: {\n    link_color: string\n    button_color: string\n    button_text_color: string\n    secondary_bg_color: string\n    hint_color: string\n    bg_color: string\n    text_color: string\n  }\n  isExpanded: boolean\n  viewportHeight: number\n  viewportStableHeight: number\n  headerColor: string\n  backgroundColor: string\n  BackButton: {\n    isVisible: boolean\n    show(): void\n    hide(): void\n    onClick(callback: () => void): void\n    offClick(callback: () => void): void\n  }\n  MainButton: {\n    text: string\n    color: string\n    textColor: string\n    isVisible: boolean\n    isActive: boolean\n    isProgressVisible: boolean\n    setText(text: string): void\n    onClick(callback: () => void): void\n    offClick(callback: () => void): void\n    show(): void\n    hide(): void\n    enable(): void\n    disable(): void\n    showProgress(leaveActive?: boolean): void\n    hideProgress(): void\n    setParams(params: {\n      text?: string\n      color?: string\n      text_color?: string\n      is_active?: boolean\n      is_visible?: boolean\n    }): void\n  }\n  HapticFeedback: {\n    impactOccurred(style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft'): void\n    notificationOccurred(type: 'error' | 'success' | 'warning'): void\n    selectionChanged(): void\n  }\n  expand(): void\n  close(): void\n  ready(): void\n}\n\ndeclare global {\n  interface Window {\n    Telegram?: {\n      WebApp: TelegramWebApp\n    }\n  }\n}\n\nexport function useTelegramWebApp() {\n  const [webApp, setWebApp] = useState<TelegramWebApp | null>(null)\n  const [user, setUser] = useState<TelegramUser | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Проверяем, доступен ли Telegram WebApp\n    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {\n      const tg = window.Telegram.WebApp\n      \n      // Инициализируем WebApp\n      tg.ready()\n      tg.expand()\n      \n      setWebApp(tg)\n      setUser(tg.initDataUnsafe.user || null)\n      \n      // Устанавливаем тему\n      if (tg.colorScheme === 'dark') {\n        document.documentElement.classList.add('dark')\n      }\n      \n      console.log('Telegram WebApp initialized:', {\n        user: tg.initDataUnsafe.user,\n        platform: tg.platform,\n        version: tg.version,\n        colorScheme: tg.colorScheme\n      })\n    } else {\n      // Режим разработки - создаем фиктивного пользователя\n      console.log('Telegram WebApp not available, using demo mode')\n      const demoUser: TelegramUser = {\n        id: parseInt(localStorage.getItem('telegram_user_id') || Date.now().toString()),\n        first_name: 'Demo',\n        last_name: 'User',\n        username: 'demo_user',\n        language_code: 'ru'\n      }\n      setUser(demoUser)\n    }\n    \n    setIsLoading(false)\n  }, [])\n\n  // Функция для получения реального telegram_id\n  const getTelegramId = (): string => {\n    if (user) {\n      return user.id.toString()\n    }\n\n    // Fallback для режима разработки - только числовые ID\n    const existingId = localStorage.getItem('telegram_user_id')\n\n    // Проверяем, что ID числовой и валидный\n    if (!existingId || existingId.includes('demo_user_') || existingId.includes('user_') || isNaN(Number(existingId))) {\n      const newId = Date.now().toString()\n      localStorage.setItem('telegram_user_id', newId)\n      return newId\n    }\n\n    return existingId\n  }\n\n  // Функция для показа уведомления\n  const showNotification = (type: 'error' | 'success' | 'warning') => {\n    if (webApp?.HapticFeedback) {\n      webApp.HapticFeedback.notificationOccurred(type)\n    }\n  }\n\n  // Функция для тактильной обратной связи\n  const hapticFeedback = (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft' = 'light') => {\n    if (webApp?.HapticFeedback) {\n      webApp.HapticFeedback.impactOccurred(style)\n    }\n  }\n\n  // Функция для закрытия приложения\n  const closeApp = () => {\n    if (webApp) {\n      webApp.close()\n    }\n  }\n\n  return {\n    webApp,\n    user,\n    isLoading,\n    getTelegramId,\n    showNotification,\n    hapticFeedback,\n    closeApp,\n    isInTelegram: !!webApp\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAuFO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,IAAI,gBAAkB,eAAe,OAAO,QAAQ,EAAE,QAAQ;;QAqB9D,OAAO;YACL,qDAAqD;YACrD,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAyB;gBAC7B,IAAI,SAAS,aAAa,OAAO,CAAC,uBAAuB,KAAK,GAAG,GAAG,QAAQ;gBAC5E,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,eAAe;YACjB;YACA,QAAQ;QACV;QAEA,aAAa;IACf,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,gBAAgB;QACpB,IAAI,MAAM;YACR,OAAO,KAAK,EAAE,CAAC,QAAQ;QACzB;QAEA,sDAAsD;QACtD,MAAM,aAAa,aAAa,OAAO,CAAC;QAExC,wCAAwC;QACxC,IAAI,CAAC,cAAc,WAAW,QAAQ,CAAC,iBAAiB,WAAW,QAAQ,CAAC,YAAY,MAAM,OAAO,cAAc;YACjH,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;YACjC,aAAa,OAAO,CAAC,oBAAoB;YACzC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,gBAAgB;YAC1B,OAAO,cAAc,CAAC,oBAAoB,CAAC;QAC7C;IACF;IAEA,wCAAwC;IACxC,MAAM,iBAAiB,CAAC,QAAyD,OAAO;QACtF,IAAI,QAAQ,gBAAgB;YAC1B,OAAO,cAAc,CAAC,cAAc,CAAC;QACvC;IACF;IAEA,kCAAkC;IAClC,MAAM,WAAW;QACf,IAAI,QAAQ;YACV,OAAO,KAAK;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,cAAc,CAAC,CAAC;IAClB;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/NewHomePage.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { BookOpen, Play, Star, Trophy, User, Settings, Moon, Sun, LogOut } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from '@/contexts/ThemeContext'\nimport { useUser } from '@/contexts/UserContext'\n\ninterface Lesson {\n  id: string\n  title: string\n  level: string\n  order: number\n  description: string\n}\n\ninterface UserType {\n  id: string\n  telegram_id: string\n  nickname: string\n  avatar: string\n  level: string\n  theme: string\n  is_onboarded: boolean\n  total_xp: number\n  current_streak: number\n  last_activity_date: string\n}\n\ninterface NewHomePageProps {\n  user: UserType\n}\n\ninterface ProgressData {\n  user: UserType\n  lessons: Lesson[]\n  progress: any[]\n  stats: {\n    totalLessons: number\n    completedLessons: number\n    totalXP: number\n    currentStreak: number\n    nextLesson: Lesson | null\n  }\n}\n\nexport function NewHomePage({ user }: NewHomePageProps) {\n  const { theme, toggleTheme } = useTheme()\n  const { updateUser, logout } = useUser()\n  const [progressData, setProgressData] = useState<ProgressData | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [showProfile, setShowProfile] = useState(false)\n\n  useEffect(() => {\n    loadUserProgress()\n  }, [user.telegram_id])\n\n  const loadUserProgress = async () => {\n    try {\n      const response = await fetch(`/api/users/progress?telegram_id=${user.telegram_id}`)\n      const data = await response.json()\n\n      if (response.ok) {\n        setProgressData(data)\n      } else {\n        console.error('Error loading progress:', data.error)\n        // Fallback: загружаем только уроки\n        await loadLessonsOnly()\n      }\n    } catch (error) {\n      console.error('Error loading progress:', error)\n      await loadLessonsOnly()\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadLessonsOnly = async () => {\n    try {\n      const response = await fetch('/api/lessons')\n      const data = await response.json()\n\n      if (data.lessons) {\n        const userLessons = data.lessons.filter((lesson: Lesson) => lesson.level === user.level)\n        setProgressData({\n          user,\n          lessons: userLessons,\n          progress: [],\n          stats: {\n            totalLessons: userLessons.length,\n            completedLessons: 0,\n            totalXP: user.total_xp,\n            currentStreak: user.current_streak,\n            nextLesson: userLessons[0] || null\n          }\n        })\n      }\n    } catch (error) {\n      console.error('Error loading lessons:', error)\n    }\n  }\n\n  const handleThemeToggle = async () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light'\n    toggleTheme()\n    await updateUser({ theme: newTheme })\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-300\">Загружаем уроки...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-md mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"text-2xl\">{user.avatar}</div>\n              <div>\n                <h1 className=\"font-semibold text-gray-900 dark:text-white\">\n                  Привет, {user.nickname}!\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                  Уровень: {user.level} • XP: {user.total_xp}\n                </p>\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleThemeToggle}\n                className=\"p-2\"\n              >\n                {theme === 'light' ? (\n                  <Moon className=\"h-4 w-4\" />\n                ) : (\n                  <Sun className=\"h-4 w-4\" />\n                )}\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowProfile(!showProfile)}\n                className=\"p-2\"\n              >\n                <User className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-md mx-auto p-4 space-y-6\">\n        {/* Welcome Back Message */}\n        {progressData && progressData.stats.completedLessons > 0 && (\n          <Card className=\"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-700\">\n            <CardContent className=\"p-4 text-center\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                С возвращением, {user.nickname}! 👋\n              </h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                Ты уже прошел {progressData.stats.completedLessons} из {progressData.stats.totalLessons} уроков.\n                {progressData.stats.nextLesson ? ' Продолжай изучение!' : ' Отличная работа!'}\n              </p>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Profile Card (показывается при клике на User) */}\n        {showProfile && (\n          <Card className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white\">\n            <CardContent className=\"p-6 text-center\">\n              <div className=\"text-4xl mb-3\">{user.avatar}</div>\n              <h2 className=\"text-xl font-bold mb-2\">{user.nickname}</h2>\n              <div className=\"space-y-2 text-sm opacity-90\">\n                <p>Уровень: {user.level}</p>\n                <p>Общий XP: {user.total_xp}</p>\n                <p>Текущая серия: {user.current_streak} дней</p>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={logout}\n                className=\"mt-4 text-white border-white hover:bg-white hover:text-blue-600\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Выйти\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Progress Card */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Твой прогресс\n              </h2>\n              <Trophy className=\"h-5 w-5 text-yellow-500\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-3 gap-4 text-center\">\n              <div>\n                <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                  {progressData?.stats.totalXP || 0}\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-300\">XP</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                  {progressData?.stats.completedLessons || 0}\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-300\">Пройдено</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">\n                  {progressData?.stats.totalLessons || 0}\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-300\">Всего уроков</div>\n              </div>\n            </div>\n\n            {/* Progress Bar */}\n            {progressData && progressData.stats.totalLessons > 0 && (\n              <div className=\"mt-4\">\n                <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                  <span>Прогресс изучения</span>\n                  <span>{Math.round((progressData.stats.completedLessons / progressData.stats.totalLessons) * 100)}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300\"\n                    style={{\n                      width: `${(progressData.stats.completedLessons / progressData.stats.totalLessons) * 100}%`\n                    }}\n                  ></div>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Next Lesson Card */}\n        {progressData?.stats.nextLesson && (\n          <Card className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-700\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  Следующий урок\n                </h2>\n                <Play className=\"h-5 w-5 text-green-500\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <Link href={`/lesson/${progressData.stats.nextLesson.id}`}>\n                <div className=\"flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg hover:shadow-md transition-shadow cursor-pointer\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-full text-sm font-medium\">\n                      {progressData.stats.nextLesson.order}\n                    </div>\n                    <div>\n                      <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                        {progressData.stats.nextLesson.title}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                        {progressData.stats.nextLesson.description}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-sm font-medium text-green-600 dark:text-green-400\">\n                      Начать\n                    </span>\n                    <Play className=\"h-4 w-4 text-green-500\" />\n                  </div>\n                </div>\n              </Link>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* All Lessons */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Все уроки уровня {user.level}\n              </h2>\n              <BookOpen className=\"h-5 w-5 text-blue-500\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            {!progressData || progressData.lessons.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n                  Пока нет уроков для уровня {user.level}\n                </p>\n                <Link href=\"/admin\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    Добавить уроки\n                  </Button>\n                </Link>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {progressData.lessons.map((lesson) => {\n                  const isCompleted = progressData.progress.some(p => p.lesson_id === lesson.id && p.completed)\n                  const isNext = progressData.stats.nextLesson?.id === lesson.id\n\n                  return (\n                    <Link key={lesson.id} href={`/lesson/${lesson.id}`}>\n                      <div className={`flex items-center justify-between p-4 rounded-lg transition-colors cursor-pointer ${\n                        isCompleted\n                          ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700'\n                          : isNext\n                          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700'\n                          : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600'\n                      }`}>\n                        <div className=\"flex items-center space-x-3\">\n                          <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${\n                            isCompleted\n                              ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'\n                              : isNext\n                              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'\n                              : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300'\n                          }`}>\n                            {isCompleted ? '✓' : lesson.order}\n                          </div>\n                          <div>\n                            <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                              {lesson.title}\n                            </h3>\n                            <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                              {lesson.description}\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          {isCompleted && (\n                            <span className=\"text-xs font-medium text-green-600 dark:text-green-400\">\n                              Пройден\n                            </span>\n                          )}\n                          {isNext && (\n                            <span className=\"text-xs font-medium text-blue-600 dark:text-blue-400\">\n                              Следующий\n                            </span>\n                          )}\n                          <Play className={`h-5 w-5 ${\n                            isCompleted ? 'text-green-400' : isNext ? 'text-blue-400' : 'text-gray-400'\n                          }`} />\n                        </div>\n                      </div>\n                    </Link>\n                  )\n                })}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <Link href=\"/test-memory-match\">\n            <Card className=\"cursor-pointer hover:shadow-md transition-shadow\">\n              <CardContent className=\"p-4 text-center\">\n                <Star className=\"h-8 w-8 text-yellow-500 mx-auto mb-2\" />\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Тест игры\n                </p>\n              </CardContent>\n            </Card>\n          </Link>\n          \n          <Link href=\"/admin\">\n            <Card className=\"cursor-pointer hover:shadow-md transition-shadow\">\n              <CardContent className=\"p-4 text-center\">\n                <Settings className=\"h-8 w-8 text-gray-500 mx-auto mb-2\" />\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Админ панель\n                </p>\n              </CardContent>\n            </Card>\n          </Link>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAgDO,SAAS,YAAY,EAAE,IAAI,EAAoB;IACpD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,KAAK,WAAW;KAAC;IAErB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,KAAK,WAAW,EAAE;YAClF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB;YAClB,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,KAAK,KAAK;gBACnD,mCAAmC;gBACnC,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,cAAc,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,SAAmB,OAAO,KAAK,KAAK,KAAK,KAAK;gBACvF,gBAAgB;oBACd;oBACA,SAAS;oBACT,UAAU,EAAE;oBACZ,OAAO;wBACL,cAAc,YAAY,MAAM;wBAChC,kBAAkB;wBAClB,SAAS,KAAK,QAAQ;wBACtB,eAAe,KAAK,cAAc;wBAClC,YAAY,WAAW,CAAC,EAAE,IAAI;oBAChC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C;QACA,MAAM,WAAW;YAAE,OAAO;QAAS;IACrC;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,KAAK,MAAM;;;;;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA8C;oDACjD,KAAK,QAAQ;oDAAC;;;;;;;0DAEzB,8OAAC;gDAAE,WAAU;;oDAA2C;oDAC5C,KAAK,KAAK;oDAAC;oDAAQ,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;0CAMhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;iEAEhB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAInB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1B,8OAAC;gBAAI,WAAU;;oBAEZ,gBAAgB,aAAa,KAAK,CAAC,gBAAgB,GAAG,mBACrD,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAG,WAAU;;wCAA2D;wCACtD,KAAK,QAAQ;wCAAC;;;;;;;8CAEjC,8OAAC;oCAAE,WAAU;;wCAA2C;wCACvC,aAAa,KAAK,CAAC,gBAAgB;wCAAC;wCAAK,aAAa,KAAK,CAAC,YAAY;wCAAC;wCACvF,aAAa,KAAK,CAAC,UAAU,GAAG,yBAAyB;;;;;;;;;;;;;;;;;;oBAOjE,6BACC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CAAiB,KAAK,MAAM;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CAA0B,KAAK,QAAQ;;;;;;8CACrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAU,KAAK,KAAK;;;;;;;sDACvB,8OAAC;;gDAAE;gDAAW,KAAK,QAAQ;;;;;;;sDAC3B,8OAAC;;gDAAE;gDAAgB,KAAK,cAAc;gDAAC;;;;;;;;;;;;;8CAEzC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGtB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,cAAc,MAAM,WAAW;;;;;;kEAElC,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,cAAc,MAAM,oBAAoB;;;;;;kEAE3C,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,cAAc,MAAM,gBAAgB;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;oCAK7D,gBAAgB,aAAa,KAAK,CAAC,YAAY,GAAG,mBACjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAM,KAAK,KAAK,CAAC,AAAC,aAAa,KAAK,CAAC,gBAAgB,GAAG,aAAa,KAAK,CAAC,YAAY,GAAI;4DAAK;;;;;;;;;;;;;0DAEnG,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,GAAG,AAAC,aAAa,KAAK,CAAC,gBAAgB,GAAG,aAAa,KAAK,CAAC,YAAY,GAAI,IAAI,CAAC,CAAC;oDAC5F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASX,cAAc,MAAM,4BACnB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGpB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE;8CACvD,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,aAAa,KAAK,CAAC,UAAU,CAAC,KAAK;;;;;;kEAEtC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,aAAa,KAAK,CAAC,UAAU,CAAC,KAAK;;;;;;0EAEtC,8OAAC;gEAAE,WAAU;0EACV,aAAa,KAAK,CAAC,UAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;0DAIhD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAyD;;;;;;kEAGzE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAsD;gDAChD,KAAK,KAAK;;;;;;;sDAE9B,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,8OAAC,gIAAA,CAAA,cAAW;0CACT,CAAC,gBAAgB,aAAa,OAAO,CAAC,MAAM,KAAK,kBAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAE,WAAU;;gDAAwC;gDACvB,KAAK,KAAK;;;;;;;sDAExC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAAK;;;;;;;;;;;;;;;;yDAMxC,8OAAC;oCAAI,WAAU;8CACZ,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC;wCACzB,MAAM,cAAc,aAAa,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,OAAO,EAAE,IAAI,EAAE,SAAS;wCAC5F,MAAM,SAAS,aAAa,KAAK,CAAC,UAAU,EAAE,OAAO,OAAO,EAAE;wCAE9D,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAAiB,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;sDAChD,cAAA,8OAAC;gDAAI,WAAW,CAAC,kFAAkF,EACjG,cACI,mFACA,SACA,+EACA,wEACJ;;kEACA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,0EAA0E,EACzF,cACI,sEACA,SACA,kEACA,iEACJ;0EACC,cAAc,MAAM,OAAO,KAAK;;;;;;0EAEnC,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFACX,OAAO,KAAK;;;;;;kFAEf,8OAAC;wEAAE,WAAU;kFACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;kEAIzB,8OAAC;wDAAI,WAAU;;4DACZ,6BACC,8OAAC;gEAAK,WAAU;0EAAyD;;;;;;4DAI1E,wBACC,8OAAC;gEAAK,WAAU;0EAAuD;;;;;;0EAIzE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,QAAQ,EACxB,cAAc,mBAAmB,SAAS,kBAAkB,iBAC5D;;;;;;;;;;;;;;;;;;2CAxCG,OAAO,EAAE;;;;;oCA6CxB;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;0CAOvE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjF", "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useUser } from '@/contexts/UserContext'\nimport { useTelegramWebApp } from '@/hooks/useTelegramWebApp'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { NewHomePage } from '@/components/NewHomePage'\n\nexport default function Home() {\n  const router = useRouter()\n  const { user, isLoading, isFirstTime, checkUser } = useUser()\n  const { getTelegramId, isInTelegram, isLoading: telegramLoading } = useTelegramWebApp()\n  const [initTimeout, setInitTimeout] = useState(false)\n\n  useEffect(() => {\n    const initializeUser = async () => {\n      try {\n        // Принудительно очищаем старые строковые ID\n        const existingId = localStorage.getItem('telegram_user_id')\n        if (existingId && (existingId.includes('demo_user_') || existingId.includes('user_'))) {\n          console.log('Clearing old string telegram_id:', existingId)\n          localStorage.removeItem('telegram_user_id')\n        }\n\n        // Получаем telegram_id из Telegram WebApp API или используем fallback\n        const telegramId = getTelegramId()\n\n        console.log('Initializing with telegram_id:', telegramId, 'Is in Telegram:', isInTelegram)\n\n        // Валидация telegram_id - только числовые ID\n        if (!telegramId || telegramId === 'null' || telegramId === 'undefined' || isNaN(Number(telegramId))) {\n          console.log('Invalid telegram_id, generating new numeric one')\n          const newId = Date.now().toString()\n          localStorage.setItem('telegram_user_id', newId)\n          await checkUser(newId)\n          return\n        }\n\n        // Сохраняем в localStorage для последующих сессий\n        localStorage.setItem('telegram_user_id', telegramId)\n\n        // Проверяем пользователя в базе данных\n        await checkUser(telegramId)\n      } catch (error) {\n        console.error('Error initializing user:', error)\n        // При ошибке создаем новый числовой ID\n        const newId = Date.now().toString()\n        localStorage.setItem('telegram_user_id', newId)\n        await checkUser(newId)\n      }\n    }\n\n    if (!telegramLoading) {\n      initializeUser()\n    }\n  }, [getTelegramId, isInTelegram, checkUser, telegramLoading])\n\n  // Таймаут для предотвращения бесконечной загрузки\n  useEffect(() => {\n    const timeout = setTimeout(() => {\n      if (isLoading) {\n        console.log('Loading timeout reached, forcing redirect to welcome')\n        setInitTimeout(true)\n        // Принудительно устанавливаем как нового пользователя\n        const newId = Date.now().toString()\n        localStorage.setItem('telegram_user_id', newId)\n        router.push('/welcome')\n      }\n    }, 10000) // 10 секунд таймаут\n\n    return () => clearTimeout(timeout)\n  }, [isLoading, router])\n\n  // Показываем загрузку\n  if ((isLoading || telegramLoading) && !initTimeout) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <LoadingSpinner />\n          <p className=\"text-gray-600 dark:text-gray-300 mt-4\">Загружаем ваш профиль...</p>\n          <p className=\"text-gray-500 dark:text-gray-400 mt-2 text-sm\">\n            Если загрузка затянулась, попробуйте обновить страницу\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // Если пользователь новый и нет пользователя в контексте, перенаправляем на страницу приветствия\n  if (isFirstTime && !user) {\n    router.push('/welcome')\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <LoadingSpinner />\n          <p className=\"text-gray-600 dark:text-gray-300 mt-4\">Перенаправляем...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Если пользователь существует, показываем главную страницу\n  if (user) {\n    return <NewHomePage user={user} />\n  }\n\n  // Fallback или таймаут\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"text-center max-w-md mx-auto p-6\">\n        <h1 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {initTimeout ? 'Что-то пошло не так' : 'Добро пожаловать!'}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n          {initTimeout\n            ? 'Загрузка заняла слишком много времени. Попробуйте начать заново.'\n            : 'Настраиваем ваш аккаунт...'\n          }\n        </p>\n        {initTimeout && (\n          <div className=\"space-y-3\">\n            <button\n              onClick={() => {\n                localStorage.clear()\n                window.location.reload()\n              }}\n              className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Начать заново\n            </button>\n            <button\n              onClick={() => router.push('/welcome')}\n              className=\"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n            >\n              Перейти к регистрации\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1D,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IACpF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,4CAA4C;gBAC5C,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,cAAc,CAAC,WAAW,QAAQ,CAAC,iBAAiB,WAAW,QAAQ,CAAC,QAAQ,GAAG;oBACrF,QAAQ,GAAG,CAAC,oCAAoC;oBAChD,aAAa,UAAU,CAAC;gBAC1B;gBAEA,sEAAsE;gBACtE,MAAM,aAAa;gBAEnB,QAAQ,GAAG,CAAC,kCAAkC,YAAY,mBAAmB;gBAE7E,6CAA6C;gBAC7C,IAAI,CAAC,cAAc,eAAe,UAAU,eAAe,eAAe,MAAM,OAAO,cAAc;oBACnG,QAAQ,GAAG,CAAC;oBACZ,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;oBACjC,aAAa,OAAO,CAAC,oBAAoB;oBACzC,MAAM,UAAU;oBAChB;gBACF;gBAEA,kDAAkD;gBAClD,aAAa,OAAO,CAAC,oBAAoB;gBAEzC,uCAAuC;gBACvC,MAAM,UAAU;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,uCAAuC;gBACvC,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;gBACjC,aAAa,OAAO,CAAC,oBAAoB;gBACzC,MAAM,UAAU;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;YACpB;QACF;IACF,GAAG;QAAC;QAAe;QAAc;QAAW;KAAgB;IAE5D,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW;YACzB,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC;gBACZ,eAAe;gBACf,sDAAsD;gBACtD,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;gBACjC,aAAa,OAAO,CAAC,oBAAoB;gBACzC,OAAO,IAAI,CAAC;YACd;QACF,GAAG,OAAO,oBAAoB;;QAE9B,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAW;KAAO;IAEtB,sBAAsB;IACtB,IAAI,CAAC,aAAa,eAAe,KAAK,CAAC,aAAa;QAClD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,iBAAc;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;;;;;;IAMrE;IAEA,iGAAiG;IACjG,IAAI,eAAe,CAAC,MAAM;QACxB,OAAO,IAAI,CAAC;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,iBAAc;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;;;;;;IAI7D;IAEA,4DAA4D;IAC5D,IAAI,MAAM;QACR,qBAAO,8OAAC,iIAAA,CAAA,cAAW;YAAC,MAAM;;;;;;IAC5B;IAEA,uBAAuB;IACvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BACX,cAAc,wBAAwB;;;;;;8BAEzC,8OAAC;oBAAE,WAAU;8BACV,cACG,qEACA;;;;;;gBAGL,6BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;gCACP,aAAa,KAAK;gCAClB,OAAO,QAAQ,CAAC,MAAM;4BACxB;4BACA,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}