'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/contexts/UserContext'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { NewHomePage } from '@/components/NewHomePage'

export default function Home() {
  const router = useRouter()
  const { user, isLoading, isFirstTime } = useUser()

  useEffect(() => {
    // Симулируем получение telegram_id (в реальном приложении это будет из Telegram WebApp API)
    if (!localStorage.getItem('telegram_user_id')) {
      localStorage.setItem('telegram_user_id', Date.now().toString())
    }
  }, [])

  // Показываем загрузку
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Загружаем ваш профиль...</p>
        </div>
      </div>
    )
  }

  // Если пользователь новый, перенаправляем на страницу приветствия
  if (isFirstTime) {
    router.push('/welcome')
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Перенаправляем...</p>
        </div>
      </div>
    )
  }

  // Если пользователь существует, показываем главную страницу
  if (user) {
    return <NewHomePage user={user} />
  }

  // Fallback
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Добро пожаловать!
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Настраиваем ваш аккаунт...
        </p>
      </div>
    </div>
  )
}
