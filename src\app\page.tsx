'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/contexts/UserContext'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { NewHomePage } from '@/components/NewHomePage'

export default function Home() {
  const router = useRouter()
  const { user, isLoading, isFirstTime } = useUser()

  useEffect(() => {
    // Симулируем получение telegram_id (в реальном приложении это будет из Telegram WebApp API)
    const existingId = localStorage.getItem('telegram_user_id')

    // Если ID содержит строку "demo_user_", очищаем и создаем новый числовой ID
    if (!existingId || existingId.includes('demo_user_')) {
      const newId = Date.now().toString()
      localStorage.setItem('telegram_user_id', newId)
      console.log('Generated new telegram_id:', newId)
    }
  }, [])

  // Дополнительная проверка для предотвращения циклических перенаправлений
  useEffect(() => {
    if (user && isFirstTime) {
      // Если пользователь существует, но isFirstTime все еще true, исправляем это
      console.log('User exists but isFirstTime is true, fixing state')
    }
  }, [user, isFirstTime])

  // Показываем загрузку
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Загружаем ваш профиль...</p>
        </div>
      </div>
    )
  }

  // Если пользователь новый и нет пользователя в контексте, перенаправляем на страницу приветствия
  if (isFirstTime && !user) {
    router.push('/welcome')
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Перенаправляем...</p>
        </div>
      </div>
    )
  }

  // Если пользователь существует, показываем главную страницу
  if (user) {
    return <NewHomePage user={user} />
  }

  // Fallback
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Добро пожаловать!
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Настраиваем ваш аккаунт...
        </p>
      </div>
    </div>
  )
}
