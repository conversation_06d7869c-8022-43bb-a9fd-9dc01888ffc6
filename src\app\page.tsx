'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/contexts/UserContext'
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { NewHomePage } from '@/components/NewHomePage'

export default function Home() {
  const router = useRouter()
  const { user, isLoading, isFirstTime, checkUser } = useUser()
  const { getTelegramId, isInTelegram, isLoading: telegramLoading } = useTelegramWebApp()
  const [initTimeout, setInitTimeout] = useState(false)

  useEffect(() => {
    const initializeUser = async () => {
      try {
        console.log('Starting user initialization...')

        // ПРИНУДИТЕЛЬНО очищаем ВСЕ старые данные
        const existingId = localStorage.getItem('telegram_user_id')
        console.log('Existing ID in localStorage:', existingId)

        // Очищаем любые проблемные ID
        if (existingId && (
          existingId.includes('demo_user_') ||
          existingId.includes('user_') ||
          existingId === 'null' ||
          existingId === 'undefined' ||
          existingId.length < 10 // Слишком короткий ID
        )) {
          console.log('Clearing problematic telegram_id:', existingId)
          localStorage.clear() // Полная очистка
        }

        // Создаем новый числовой ID
        const newId = Date.now().toString()
        console.log('Generated new telegram_id:', newId)
        localStorage.setItem('telegram_user_id', newId)

        // Проверяем пользователя в базе данных
        console.log('Checking user with ID:', newId)
        await checkUser(newId)

      } catch (error) {
        console.error('Error initializing user:', error)
        // При любой ошибке - полная очистка и новый ID
        localStorage.clear()
        const fallbackId = Date.now().toString()
        localStorage.setItem('telegram_user_id', fallbackId)
        console.log('Fallback ID created:', fallbackId)
        await checkUser(fallbackId)
      }
    }

    // Запускаем инициализацию сразу, не ждем Telegram
    initializeUser()
  }, [checkUser])

  // Таймаут для предотвращения бесконечной загрузки
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.log('Loading timeout reached, forcing redirect to welcome')
        setInitTimeout(true)
        // Принудительно устанавливаем как нового пользователя
        const newId = Date.now().toString()
        localStorage.setItem('telegram_user_id', newId)
        router.push('/welcome')
      }
    }, 10000) // 10 секунд таймаут

    return () => clearTimeout(timeout)
  }, [isLoading, router])

  // Показываем загрузку с кнопкой экстренного сброса
  if ((isLoading || telegramLoading) && !initTimeout) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center max-w-md mx-auto p-6">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4 mb-6">Загружаем ваш профиль...</p>
          <p className="text-gray-500 dark:text-gray-400 mt-2 text-sm mb-6">
            Если загрузка затянулась, попробуйте обновить страницу
          </p>

          {/* Кнопка экстренного сброса */}
          <div className="space-y-3">
            <button
              onClick={() => {
                console.log('🚨 Emergency reset triggered')
                localStorage.clear()
                window.location.href = '/welcome'
              }}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
            >
              🚨 Экстренный сброс
            </button>
            <button
              onClick={() => {
                console.log('🔄 Force refresh triggered')
                window.location.reload()
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              🔄 Обновить страницу
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Если пользователь новый и нет пользователя в контексте, перенаправляем на страницу приветствия
  if (isFirstTime && !user) {
    router.push('/welcome')
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Перенаправляем...</p>
        </div>
      </div>
    )
  }

  // Если пользователь существует, показываем главную страницу
  if (user) {
    return <NewHomePage user={user} />
  }

  // Fallback или таймаут
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="text-center max-w-md mx-auto p-6">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {initTimeout ? 'Что-то пошло не так' : 'Добро пожаловать!'}
        </h1>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {initTimeout
            ? 'Загрузка заняла слишком много времени. Попробуйте начать заново.'
            : 'Настраиваем ваш аккаунт...'
          }
        </p>
        {initTimeout && (
          <div className="space-y-3">
            <button
              onClick={() => {
                localStorage.clear()
                window.location.reload()
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Начать заново
            </button>
            <button
              onClick={() => router.push('/welcome')}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Перейти к регистрации
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
