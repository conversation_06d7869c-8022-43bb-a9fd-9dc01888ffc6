'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/contexts/UserContext'
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { NewHomePage } from '@/components/NewHomePage'

export default function Home() {
  const router = useRouter()
  const { user, isLoading, isFirstTime, checkUser } = useUser()
  const { getTelegramId, isInTelegram, isLoading: telegramLoading } = useTelegramWebApp()

  useEffect(() => {
    // Получаем telegram_id из Telegram WebApp API или используем fallback
    const telegramId = getTelegramId()

    // Сохраняем в localStorage для последующих сессий
    localStorage.setItem('telegram_user_id', telegramId)

    // Проверяем пользователя в базе данных
    checkUser(telegramId)

    console.log('Telegram ID:', telegramId, 'Is in Telegram:', isInTelegram)
  }, [getTelegramId, isInTelegram, checkUser])

  // Показываем загрузку
  if (isLoading || telegramLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Загружаем ваш профиль...</p>
        </div>
      </div>
    )
  }

  // Если пользователь новый и нет пользователя в контексте, перенаправляем на страницу приветствия
  if (isFirstTime && !user) {
    router.push('/welcome')
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Перенаправляем...</p>
        </div>
      </div>
    )
  }

  // Если пользователь существует, показываем главную страницу
  if (user) {
    return <NewHomePage user={user} />
  }

  // Fallback
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Добро пожаловать!
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Настраиваем ваш аккаунт...
        </p>
      </div>
    </div>
  )
}
