'use client'

import { useAuth } from '@/contexts/AuthContext'
import { HomePage } from '@/components/HomePage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export default function Home() {
  const { user, loading, error } = useAuth()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-red-600 mb-2">Ошибка</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Добро пожаловать!</h1>
          <p className="text-gray-600">Пожалуйста, подождите, пока мы настраиваем ваш аккаунт...</p>
        </div>
      </div>
    )
  }

  return <HomePage user={user} />
}
