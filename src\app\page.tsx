'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/contexts/UserContext'
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { NewHomePage } from '@/components/NewHomePage'

export default function Home() {
  const router = useRouter()
  const { user, isLoading, isFirstTime, checkUser } = useUser()
  const { getTelegramId, isInTelegram, isLoading: telegramLoading } = useTelegramWebApp()
  const [initTimeout, setInitTimeout] = useState(false)

  useEffect(() => {
    const initializeUser = async () => {
      try {
        // Принудительно очищаем старые строковые ID
        const existingId = localStorage.getItem('telegram_user_id')
        if (existingId && (existingId.includes('demo_user_') || existingId.includes('user_'))) {
          console.log('Clearing old string telegram_id:', existingId)
          localStorage.removeItem('telegram_user_id')
        }

        // Получаем telegram_id из Telegram WebApp API или используем fallback
        const telegramId = getTelegramId()

        console.log('Initializing with telegram_id:', telegramId, 'Is in Telegram:', isInTelegram)

        // Валидация telegram_id - только числовые ID
        if (!telegramId || telegramId === 'null' || telegramId === 'undefined' || isNaN(Number(telegramId))) {
          console.log('Invalid telegram_id, generating new numeric one')
          const newId = Date.now().toString()
          localStorage.setItem('telegram_user_id', newId)
          await checkUser(newId)
          return
        }

        // Сохраняем в localStorage для последующих сессий
        localStorage.setItem('telegram_user_id', telegramId)

        // Проверяем пользователя в базе данных
        await checkUser(telegramId)
      } catch (error) {
        console.error('Error initializing user:', error)
        // При ошибке создаем новый числовой ID
        const newId = Date.now().toString()
        localStorage.setItem('telegram_user_id', newId)
        await checkUser(newId)
      }
    }

    if (!telegramLoading) {
      initializeUser()
    }
  }, [getTelegramId, isInTelegram, checkUser, telegramLoading])

  // Таймаут для предотвращения бесконечной загрузки
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.log('Loading timeout reached, forcing redirect to welcome')
        setInitTimeout(true)
        // Принудительно устанавливаем как нового пользователя
        const newId = Date.now().toString()
        localStorage.setItem('telegram_user_id', newId)
        router.push('/welcome')
      }
    }, 10000) // 10 секунд таймаут

    return () => clearTimeout(timeout)
  }, [isLoading, router])

  // Показываем загрузку
  if ((isLoading || telegramLoading) && !initTimeout) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Загружаем ваш профиль...</p>
          <p className="text-gray-500 dark:text-gray-400 mt-2 text-sm">
            Если загрузка затянулась, попробуйте обновить страницу
          </p>
        </div>
      </div>
    )
  }

  // Если пользователь новый и нет пользователя в контексте, перенаправляем на страницу приветствия
  if (isFirstTime && !user) {
    router.push('/welcome')
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner />
          <p className="text-gray-600 dark:text-gray-300 mt-4">Перенаправляем...</p>
        </div>
      </div>
    )
  }

  // Если пользователь существует, показываем главную страницу
  if (user) {
    return <NewHomePage user={user} />
  }

  // Fallback или таймаут
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="text-center max-w-md mx-auto p-6">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {initTimeout ? 'Что-то пошло не так' : 'Добро пожаловать!'}
        </h1>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {initTimeout
            ? 'Загрузка заняла слишком много времени. Попробуйте начать заново.'
            : 'Настраиваем ваш аккаунт...'
          }
        </p>
        {initTimeout && (
          <div className="space-y-3">
            <button
              onClick={() => {
                localStorage.clear()
                window.location.reload()
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Начать заново
            </button>
            <button
              onClick={() => router.push('/welcome')}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Перейти к регистрации
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
