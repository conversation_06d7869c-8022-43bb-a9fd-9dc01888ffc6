'use client'

import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, CheckCircle } from 'lucide-react'
import { getExercisesByLessonId, markExerciseComplete, updateUserXP } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'
import { Database } from '@/lib/database.types'
import { Button } from '@/components/ui/Button'
import { ProgressBar } from '@/components/ui/ProgressBar'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { QuizGame } from '@/components/exercises/QuizGame'
import { FillInTheBlankGame } from '@/components/exercises/FillInTheBlankGame'
import { WordPuzzleGame } from '@/components/exercises/WordPuzzleGame'
import { SentenceBuilderGame } from '@/components/exercises/SentenceBuilderGame'
import { ReadingGame } from '@/components/exercises/ReadingGame'
import { Memory<PERSON>atchGame } from '@/components/exercises/MemoryMatchGame'

type Exercise = Database['public']['Tables']['exercises']['Row']

export default function LessonPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const lessonId = params.id as string

  const [exercises, setExercises] = useState<Exercise[]>([])
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [completed, setCompleted] = useState(false)
  const [totalXP, setTotalXP] = useState(0)

  useEffect(() => {
    if (lessonId) {
      loadExercises()
    }
  }, [lessonId])

  const loadExercises = async () => {
    try {
      setLoading(true)
      const lessonExercises = await getExercisesByLessonId(lessonId)
      setExercises(lessonExercises)
    } catch (error) {
      console.error('Error loading exercises:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExerciseComplete = async (correct: boolean, score?: number) => {
    if (!user) return

    const currentExercise = exercises[currentExerciseIndex]
    
    if (correct && currentExercise) {
      // Mark exercise as complete
      await markExerciseComplete(
        user.id,
        lessonId,
        currentExercise.id,
        score
      )

      // Add XP
      const xpEarned = currentExercise.xp_reward
      await updateUserXP(user.id, xpEarned)
      setTotalXP(prev => prev + xpEarned)
    }

    // Move to next exercise or complete lesson
    if (currentExerciseIndex < exercises.length - 1) {
      setCurrentExerciseIndex(prev => prev + 1)
    } else {
      setCompleted(true)
    }
  }

  const renderExercise = (exercise: Exercise) => {
    const content = exercise.content_json as any

    switch (exercise.type) {
      case 'quiz':
        return (
          <QuizGame
            question={content.question}
            options={content.options}
            correct={content.correct}
            onComplete={handleExerciseComplete}
          />
        )
      case 'fill-in-the-blank':
        return (
          <FillInTheBlankGame
            sentence={content.sentence}
            options={content.options}
            correct={content.correct}
            onComplete={handleExerciseComplete}
          />
        )
      case 'word-puzzle':
        return (
          <WordPuzzleGame
            target={content.target}
            words={content.words}
            onComplete={handleExerciseComplete}
          />
        )
      case 'sentence-builder':
        return (
          <SentenceBuilderGame
            translation={content.translation}
            correctOrder={content.correct_order}
            extraWords={content.extra_words}
            onComplete={handleExerciseComplete}
          />
        )
      case 'reading':
        return (
          <ReadingGame
            text={content.text}
            questions={content.questions}
            onComplete={handleExerciseComplete}
          />
        )
      case 'memory-match':
        return (
          <MemoryMatchGame
            wordPairs={content.word_pairs}
            onComplete={handleExerciseComplete}
          />
        )
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-600">
              Exercise type "{exercise.type}" not implemented yet.
            </p>
            <Button 
              onClick={() => handleExerciseComplete(true)}
              className="mt-4"
            >
              Skip Exercise
            </Button>
          </div>
        )
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (exercises.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Упражнения не найдены
          </h1>
          <p className="text-gray-600 mb-4">
            В этом уроке пока нет упражнений.
          </p>
          <Link href="/lessons">
            <Button>Назад к урокам</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (completed) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Урок завершен!
          </h1>
          <p className="text-gray-600 mb-4">
            Отлично! Вы выполнили все упражнения в этом уроке.
          </p>
          <div className="bg-white rounded-lg p-4 mb-6">
            <div className="text-3xl font-bold text-green-600 mb-1">
              +{totalXP} XP
            </div>
            <div className="text-sm text-gray-600">Опыта получено</div>
          </div>
          <div className="space-y-3">
            <Link href="/lessons">
              <Button className="w-full">
                Назад к урокам
              </Button>
            </Link>
            <Link href="/">
              <Button variant="outline" className="w-full">
                Главная
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const currentExercise = exercises[currentExerciseIndex]
  const progress = ((currentExerciseIndex + 1) / exercises.length) * 100

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/lessons">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-lg font-semibold text-gray-900">
                Упражнение {currentExerciseIndex + 1} из {exercises.length}
              </h1>
            </div>
          </div>
          <ProgressBar
            value={currentExerciseIndex + 1}
            max={exercises.length}
            className="w-full"
          />
        </div>
      </div>

      {/* Exercise Content */}
      <div className="max-w-md mx-auto px-4 py-6">
        {renderExercise(currentExercise)}
      </div>
    </div>
  )
}
