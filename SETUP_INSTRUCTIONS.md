# 🚀 Инструкции по настройке базы данных

Ваше приложение работает, но нужно настроить базу данных Supabase. Выполните следующие шаги:

## 📋 Шаг 1: Создание таблиц

1. **Откройте Supabase Dashboard**
   - Перейдите на https://supabase.com/dashboard
   - Войдите в свой проект

2. **Откройте SQL Editor**
   - В левом меню нажмите "SQL Editor"
   - Нажмите "New query"

3. **Выполните миграцию схемы**
   - Скопируйте и выполните содержимое файла: `supabase/migrations/001_initial_schema.sql`
   - Нажмите "Run" для выполнения

## 📦 Шаг 2: Настройка хранилища

1. **В том же SQL Editor**
   - Создайте новый запрос
   - Скопируйте и выполните содержимое файла: `supabase/storage/setup.sql`
   - Нажмите "Run"

## 📝 Шаг 3: Добавление примеров данных

1. **В том же SQL Editor**
   - Создайте новый запрос
   - Скопируйте и выполните содержимое файла: `supabase/seed.sql`
   - Нажмите "Run"

## ✅ Шаг 4: Проверка

После выполнения всех шагов запустите:

```bash
node scripts/test-data.js
```

Вы должны увидеть:
- ✅ Найдены уроки
- ✅ Найдены упражнения
- ✅ Найдены карточки

## 🔧 Альтернативный способ (через Supabase CLI)

Если у вас установлен Supabase CLI:

```bash
# Инициализация проекта
supabase init

# Связывание с проектом
supabase link --project-ref qhfixfwfhjqxeqzfmdeg

# Применение миграций
supabase db push
```

## 📱 После настройки

1. **Перезапустите сервер разработки**
   ```bash
   npm run dev
   ```

2. **Откройте приложение**
   - Перейдите на http://localhost:3000
   - Вы должны увидеть реальные данные из Supabase

## 🎯 Что должно работать

После настройки базы данных:

- ✅ Аутентификация пользователей через Telegram
- ✅ Загрузка уроков по уровням (A1, A2, B1, etc.)
- ✅ Интерактивные упражнения
- ✅ Система XP и прогресса
- ✅ Сохранение результатов

## 🆘 Если что-то не работает

1. **Проверьте переменные окружения**
   ```bash
   echo $NEXT_PUBLIC_SUPABASE_URL
   echo $NEXT_PUBLIC_SUPABASE_ANON_KEY
   ```

2. **Проверьте подключение к базе**
   ```bash
   node scripts/test-data.js
   ```

3. **Проверьте логи сервера**
   - Посмотрите на вывод `npm run dev`
   - Ищите ошибки подключения к базе данных

## 📞 Поддержка

Если возникли проблемы:
1. Проверьте, что все SQL-скрипты выполнены без ошибок
2. Убедитесь, что RLS (Row Level Security) настроен правильно
3. Проверьте права доступа к таблицам

---

После выполнения этих шагов ваше приложение будет полностью функциональным! 🎉
