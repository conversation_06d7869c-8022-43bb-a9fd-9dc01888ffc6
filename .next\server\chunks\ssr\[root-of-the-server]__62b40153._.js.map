{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/telegram.ts"], "sourcesContent": ["// For now, we'll use the global Telegram WebApp object\n// import { initData, miniApp } from '@telegram-apps/sdk'\n\nexport interface TelegramUser {\n  id: number\n  first_name: string\n  last_name?: string\n  username?: string\n  language_code?: string\n  is_premium?: boolean\n}\n\nexport interface TelegramInitData {\n  user?: TelegramUser\n  auth_date: number\n  hash: string\n}\n\n// Initialize Telegram WebApp\nexport function initTelegramWebApp() {\n  if (typeof window !== 'undefined') {\n    try {\n      const webApp = window.Telegram?.WebApp\n      if (webApp) {\n        webApp.ready()\n        webApp.expand()\n        webApp.enableClosingConfirmation()\n\n        // Set theme colors\n        webApp.setHeaderColor('#1f2937') // gray-800\n        webApp.setBackgroundColor('#f9fafb') // gray-50\n\n        return true\n      }\n      return false\n    } catch (error) {\n      console.error('Failed to initialize Telegram WebApp:', error)\n      return false\n    }\n  }\n  return false\n}\n\n// Get Telegram user data\nexport function getTelegramUser(): TelegramUser | null {\n  if (typeof window === 'undefined') return null\n\n  try {\n    // Check if we're in Telegram environment\n    if (typeof window !== 'undefined' && window.Telegram?.WebApp?.initDataUnsafe?.user) {\n      console.warn('Using Telegram WebApp data')\n      return window.Telegram.WebApp.initDataUnsafe.user\n    }\n\n    // For development, return a mock user\n    console.warn('Using mock user for development')\n    return {\n      id: 123456789,\n      first_name: 'Test',\n      last_name: 'User',\n      username: 'testuser'\n    }\n  } catch (error) {\n    console.error('Failed to get Telegram user data:', error)\n    // Return mock user as fallback\n    return {\n      id: 123456789,\n      first_name: 'Test',\n      last_name: 'User',\n      username: 'testuser'\n    }\n  }\n}\n\n// Validate Telegram WebApp data (server-side)\nexport function validateTelegramData(initData: string, botToken: string): boolean {\n  if (!initData || !botToken) return false\n  \n  try {\n    const urlParams = new URLSearchParams(initData)\n    const hash = urlParams.get('hash')\n    urlParams.delete('hash')\n    \n    const dataCheckString = Array.from(urlParams.entries())\n      .sort(([a], [b]) => a.localeCompare(b))\n      .map(([key, value]) => `${key}=${value}`)\n      .join('\\n')\n    \n    const crypto = require('crypto')\n    const secretKey = crypto.createHmac('sha256', 'WebAppData').update(botToken).digest()\n    const calculatedHash = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex')\n    \n    return calculatedHash === hash\n  } catch (error) {\n    console.error('Error validating Telegram data:', error)\n    return false\n  }\n}\n\n// Check if running in Telegram WebApp environment\nexport function isTelegramWebApp(): boolean {\n  if (typeof window === 'undefined') return false\n  \n  return !!(\n    window.Telegram?.WebApp ||\n    window.location.search.includes('tgWebAppData') ||\n    window.location.hash.includes('tgWebAppData')\n  )\n}\n\n// Get Telegram WebApp instance\nexport function getTelegramWebApp() {\n  if (typeof window !== 'undefined' && window.Telegram?.WebApp) {\n    return window.Telegram.WebApp\n  }\n  return null\n}\n\n// Show Telegram alert\nexport function showTelegramAlert(message: string) {\n  try {\n    const webApp = window.Telegram?.WebApp\n    if (webApp) {\n      webApp.showAlert(message)\n    } else {\n      alert(message)\n    }\n  } catch (error) {\n    alert(message)\n  }\n}\n\n// Show Telegram confirm dialog\nexport function showTelegramConfirm(message: string, callback: (confirmed: boolean) => void) {\n  try {\n    const webApp = window.Telegram?.WebApp\n    if (webApp) {\n      webApp.showConfirm(message, callback)\n    } else {\n      const confirmed = confirm(message)\n      callback(confirmed)\n    }\n  } catch (error) {\n    const confirmed = confirm(message)\n    callback(confirmed)\n  }\n}\n\n// Haptic feedback\nexport function triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft' = 'light') {\n  try {\n    // Use the miniApp haptic feedback if available\n    if (typeof window !== 'undefined' && window.Telegram?.WebApp?.HapticFeedback) {\n      const haptic = window.Telegram.WebApp.HapticFeedback\n      switch (type) {\n        case 'light':\n          haptic.impactOccurred('light')\n          break\n        case 'medium':\n          haptic.impactOccurred('medium')\n          break\n        case 'heavy':\n          haptic.impactOccurred('heavy')\n          break\n        case 'rigid':\n          haptic.impactOccurred('rigid')\n          break\n        case 'soft':\n          haptic.impactOccurred('soft')\n          break\n      }\n    }\n  } catch (error) {\n    // Silently fail if haptic feedback is not available\n    console.debug('Haptic feedback not available:', error)\n  }\n}\n\n// Close Telegram WebApp\nexport function closeTelegramWebApp() {\n  try {\n    const webApp = window.Telegram?.WebApp\n    if (webApp) {\n      webApp.close()\n    }\n  } catch (error) {\n    console.error('Failed to close Telegram WebApp:', error)\n  }\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;AACvD,yDAAyD;;;;;;;;;;;;AAkBlD,SAAS;IACd,uCAAmC;;IAmBnC;IACA,OAAO;AACT;AAGO,SAAS;IACd,wCAAmC,OAAO;;AA2B5C;AAGO,SAAS,qBAAqB,QAAgB,EAAE,QAAgB;IACrE,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO;IAEnC,IAAI;QACF,MAAM,YAAY,IAAI,gBAAgB;QACtC,MAAM,OAAO,UAAU,GAAG,CAAC;QAC3B,UAAU,MAAM,CAAC;QAEjB,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,OAAO,IACjD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAK,EAAE,aAAa,CAAC,IACnC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EACvC,IAAI,CAAC;QAER,MAAM;QACN,MAAM,YAAY,OAAO,UAAU,CAAC,UAAU,cAAc,MAAM,CAAC,UAAU,MAAM;QACnF,MAAM,iBAAiB,OAAO,UAAU,CAAC,UAAU,WAAW,MAAM,CAAC,iBAAiB,MAAM,CAAC;QAE7F,OAAO,mBAAmB;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAO5C;AAGO,SAAS;IACd,IAAI,gBAAkB,eAAe,OAAO,QAAQ,EAAE,QAAQ;;IAE9D;IACA,OAAO;AACT;AAGO,SAAS,kBAAkB,OAAe;IAC/C,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,EAAE;QAChC,IAAI,QAAQ;YACV,OAAO,SAAS,CAAC;QACnB,OAAO;YACL,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAGO,SAAS,oBAAoB,OAAe,EAAE,QAAsC;IACzF,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,EAAE;QAChC,IAAI,QAAQ;YACV,OAAO,WAAW,CAAC,SAAS;QAC9B,OAAO;YACL,MAAM,YAAY,QAAQ;YAC1B,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,MAAM,YAAY,QAAQ;QAC1B,SAAS;IACX;AACF;AAGO,SAAS,sBAAsB,OAAwD,OAAO;IACnG,IAAI;QACF,+CAA+C;QAC/C,IAAI,gBAAkB,eAAe,OAAO,QAAQ,EAAE,QAAQ,gBAAgB;;QAmB9E;IACF,EAAE,OAAO,OAAO;QACd,oDAAoD;QACpD,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF;AAGO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,EAAE;QAChC,IAAI,QAAQ;YACV,OAAO,KAAK;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;AACF", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/MemoryMatchGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface CardData {\n  id: string\n  text: string\n  type: 'english' | 'russian'\n  pairId: string\n}\n\ninterface MemoryMatchGameProps {\n  wordPairs: Array<{ english: string; russian: string }>\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function MemoryMatchGame({\n  wordPairs,\n  onComplete\n}: MemoryMatchGameProps) {\n  const [cards, setCards] = useState<CardData[]>([])\n  const [selectedCards, setSelectedCards] = useState<string[]>([])\n  const [matchedPairs, setMatchedPairs] = useState<string[]>([])\n  const [wrongPairs, setWrongPairs] = useState<string[]>([])\n  const [gameStarted, setGameStarted] = useState(false)\n  const [gameEnded, setGameEnded] = useState(false)\n  const [score, setScore] = useState(100)\n  const [mistakes, setMistakes] = useState(0)\n\n  // Инициализация карточек\n  useEffect(() => {\n    const gameCards: CardData[] = []\n    \n    wordPairs.forEach((pair, index) => {\n      const pairId = `pair-${index}`\n      gameCards.push({\n        id: `en-${index}`,\n        text: pair.english,\n        type: 'english',\n        pairId\n      })\n      gameCards.push({\n        id: `ru-${index}`,\n        text: pair.russian,\n        type: 'russian',\n        pairId\n      })\n    })\n    \n    // Перемешиваем карточки\n    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)\n    setCards(shuffledCards)\n  }, [wordPairs])\n\n  // Проверка завершения игры\n  useEffect(() => {\n    if (matchedPairs.length === wordPairs.length && wordPairs.length > 0) {\n      setGameEnded(true)\n      setTimeout(() => {\n        onComplete(true, score)\n      }, 1500)\n    }\n  }, [matchedPairs.length, wordPairs.length, score, onComplete])\n\n  const startGame = () => {\n    setGameStarted(true)\n    triggerHapticFeedback('light')\n  }\n\n  const resetGame = () => {\n    setSelectedCards([])\n    setMatchedPairs([])\n    setWrongPairs([])\n    setGameStarted(false)\n    setGameEnded(false)\n    setScore(100)\n    setMistakes(0)\n\n    // Перемешиваем карточки заново\n    const gameCards: CardData[] = []\n    wordPairs.forEach((pair, index) => {\n      const pairId = `pair-${index}`\n      gameCards.push({\n        id: `en-${index}`,\n        text: pair.english,\n        type: 'english',\n        pairId\n      })\n      gameCards.push({\n        id: `ru-${index}`,\n        text: pair.russian,\n        type: 'russian',\n        pairId\n      })\n    })\n    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)\n    setCards(shuffledCards)\n\n    triggerHapticFeedback('light')\n  }\n\n  const handleCardClick = useCallback((cardId: string) => {\n    if (!gameStarted || gameEnded || selectedCards.includes(cardId) || matchedPairs.some(pairId => \n      cards.find(c => c.id === cardId)?.pairId === pairId\n    )) {\n      return\n    }\n\n    const newSelectedCards = [...selectedCards, cardId]\n    setSelectedCards(newSelectedCards)\n\n    if (newSelectedCards.length === 2) {\n      const [firstCardId, secondCardId] = newSelectedCards\n      const firstCard = cards.find(c => c.id === firstCardId)\n      const secondCard = cards.find(c => c.id === secondCardId)\n\n      if (firstCard && secondCard && firstCard.pairId === secondCard.pairId) {\n        // Правильная пара\n        triggerHapticFeedback('light')\n\n        // Показываем зеленый цвет на короткое время, затем скрываем карточки\n        setTimeout(() => {\n          setMatchedPairs(prev => [...prev, firstCard.pairId])\n          setSelectedCards([])\n        }, 500)\n      } else {\n        // Неправильная пара - вычитаем очки\n        setWrongPairs([firstCardId, secondCardId])\n        setMistakes(prev => prev + 1)\n        setScore(prev => Math.max(0, prev - 10)) // Штраф 10 очков за ошибку\n        triggerHapticFeedback('heavy')\n\n        setTimeout(() => {\n          setSelectedCards([])\n          setWrongPairs([])\n        }, 1000)\n      }\n    }\n  }, [gameStarted, gameEnded, selectedCards, matchedPairs, cards])\n\n\n\n  const getCardStyle = (card: CardData) => {\n    const isSelected = selectedCards.includes(card.id)\n    const isWrong = wrongPairs.includes(card.id)\n\n    // Проверяем, является ли карточка частью правильной пары (показываем зеленый цвет)\n    const isCorrectPair = selectedCards.length === 2 &&\n                         selectedCards.includes(card.id) &&\n                         !wrongPairs.length\n\n    let baseStyle = 'h-20 text-sm font-medium transition-all duration-300 rounded-lg shadow-sm '\n\n    if (isCorrectPair) {\n      baseStyle += 'bg-green-100 border-green-300 text-green-800'\n    } else if (isWrong) {\n      baseStyle += 'bg-red-100 border-red-300 text-red-800'\n    } else if (isSelected) {\n      baseStyle += 'bg-gray-100 border-gray-400 text-gray-800'\n    } else {\n      baseStyle += 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n    }\n\n    return baseStyle\n  }\n\n  if (!gameStarted) {\n    return (\n      <div className=\"space-y-6 text-center\">\n        <div>\n          <p className=\"text-gray-600 mb-4\">\n            Найдите пары: английские слова и их русские переводы\n          </p>\n          <p className=\"text-sm text-gray-500 mb-6\">\n            Начальный счёт: 100 очков. За каждую ошибку -10 очков.\n          </p>\n        </div>\n\n        <Button onClick={startGame} className=\"px-8 py-3\">\n          Начать игру\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Заголовок и счет */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">\n          Найдите пары слов\n        </h2>\n        <div className=\"flex items-center gap-4\">\n          <div className=\"flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700\">\n            <span>Счёт: {score}</span>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={resetGame}\n            className=\"p-2\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Прогресс */}\n      <div className=\"flex justify-between text-sm text-gray-600\">\n        <span>Найдено пар: {matchedPairs.length} из {wordPairs.length}</span>\n        <span>Ошибок: {mistakes}</span>\n      </div>\n\n      {/* Игровое поле */}\n      <div className=\"grid grid-cols-2 gap-3 min-h-[280px]\">\n        {cards\n          .filter(card => !matchedPairs.includes(card.pairId))\n          .map((card) => (\n            <Button\n              key={card.id}\n              variant=\"outline\"\n              className={getCardStyle(card)}\n              onClick={() => handleCardClick(card.id)}\n              disabled={gameEnded}\n            >\n              <div className=\"text-center px-2\">\n                <div className=\"font-medium\">{card.text}</div>\n              </div>\n            </Button>\n          ))}\n      </div>\n\n      {/* Результат игры */}\n      {gameEnded && (\n        <Card className=\"bg-green-50 border-green-200\">\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <CheckCircle className=\"h-8 w-8 text-green-600\" />\n            </div>\n            <h3 className=\"font-semibold mb-1 text-green-800\">\n              Отлично!\n            </h3>\n            <p className=\"text-sm text-green-700\">\n              Вы нашли все пары! Итоговый счёт: {score} очков\n            </p>\n            {mistakes > 0 && (\n              <p className=\"text-xs text-green-600 mt-1\">\n                Ошибок: {mistakes}\n              </p>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAoBO,SAAS,gBAAgB,EAC9B,SAAS,EACT,UAAU,EACW;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAwB,EAAE;QAEhC,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO;YAC9B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;YACA,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;QACF;QAEA,wBAAwB;QACxB,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC3D,SAAS;IACX,GAAG;QAAC;KAAU;IAEd,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,KAAK,UAAU,MAAM,IAAI,UAAU,MAAM,GAAG,GAAG;YACpE,aAAa;YACb,WAAW;gBACT,WAAW,MAAM;YACnB,GAAG;QACL;IACF,GAAG;QAAC,aAAa,MAAM;QAAE,UAAU,MAAM;QAAE;QAAO;KAAW;IAE7D,MAAM,YAAY;QAChB,eAAe;QACf,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;IACxB;IAEA,MAAM,YAAY;QAChB,iBAAiB,EAAE;QACnB,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,eAAe;QACf,aAAa;QACb,SAAS;QACT,YAAY;QAEZ,+BAA+B;QAC/B,MAAM,YAAwB,EAAE;QAChC,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO;YAC9B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;YACA,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;QACF;QACA,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC3D,SAAS;QAET,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;IACxB;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,eAAe,aAAa,cAAc,QAAQ,CAAC,WAAW,aAAa,IAAI,CAAC,CAAA,SACnF,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,WAAW,SAC5C;YACD;QACF;QAEA,MAAM,mBAAmB;eAAI;YAAe;SAAO;QACnD,iBAAiB;QAEjB,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,MAAM,CAAC,aAAa,aAAa,GAAG;YACpC,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC3C,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAE5C,IAAI,aAAa,cAAc,UAAU,MAAM,KAAK,WAAW,MAAM,EAAE;gBACrE,kBAAkB;gBAClB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAEtB,qEAAqE;gBACrE,WAAW;oBACT,gBAAgB,CAAA,OAAQ;+BAAI;4BAAM,UAAU,MAAM;yBAAC;oBACnD,iBAAiB,EAAE;gBACrB,GAAG;YACL,OAAO;gBACL,oCAAoC;gBACpC,cAAc;oBAAC;oBAAa;iBAAa;gBACzC,YAAY,CAAA,OAAQ,OAAO;gBAC3B,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,2BAA2B;;gBACpE,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAEtB,WAAW;oBACT,iBAAiB,EAAE;oBACnB,cAAc,EAAE;gBAClB,GAAG;YACL;QACF;IACF,GAAG;QAAC;QAAa;QAAW;QAAe;QAAc;KAAM;IAI/D,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QACjD,MAAM,UAAU,WAAW,QAAQ,CAAC,KAAK,EAAE;QAE3C,mFAAmF;QACnF,MAAM,gBAAgB,cAAc,MAAM,KAAK,KAC1B,cAAc,QAAQ,CAAC,KAAK,EAAE,KAC9B,CAAC,WAAW,MAAM;QAEvC,IAAI,YAAY;QAEhB,IAAI,eAAe;YACjB,aAAa;QACf,OAAO,IAAI,SAAS;YAClB,aAAa;QACf,OAAO,IAAI,YAAY;YACrB,aAAa;QACf,OAAO;YACL,aAAa;QACf;QAEA,OAAO;IACT;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAW,WAAU;8BAAY;;;;;;;;;;;;IAKxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCAAK;wCAAO;;;;;;;;;;;;0CAEf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAK;4BAAc,aAAa,MAAM;4BAAC;4BAAK,UAAU,MAAM;;;;;;;kCAC7D,8OAAC;;4BAAK;4BAAS;;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACZ,MACE,MAAM,CAAC,CAAA,OAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,MAAM,GACjD,GAAG,CAAC,CAAC,qBACJ,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW,aAAa;wBACxB,SAAS,IAAM,gBAAgB,KAAK,EAAE;wBACtC,UAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAe,KAAK,IAAI;;;;;;;;;;;uBAPpC,KAAK,EAAE;;;;;;;;;;YAcnB,2BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAGlD,8OAAC;4BAAE,WAAU;;gCAAyB;gCACD;gCAAM;;;;;;;wBAE1C,WAAW,mBACV,8OAAC;4BAAE,WAAU;;gCAA8B;gCAChC;;;;;;;;;;;;;;;;;;;;;;;;AAQzB", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/test-memory-match/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { MemoryMatchGame } from '@/components/exercises/MemoryMatchGame'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport Link from 'next/link'\nimport { ArrowLeft, RotateCcw } from 'lucide-react'\n\nexport default function TestMemoryMatchPage() {\n  const [gameKey, setGameKey] = useState(0)\n  const [selectedLevel, setSelectedLevel] = useState('A1')\n\n  const wordPairsByLevel = {\n    'A1': [\n      { english: \"Hello\", russian: \"Привет\" },\n      { english: \"Good morning\", russian: \"Доброе утро\" },\n      { english: \"Thank you\", russian: \"Спасибо\" },\n      { english: \"Please\", russian: \"Пожалуйста\" },\n      { english: \"Goodbye\", russian: \"До свидания\" },\n      { english: \"Yes\", russian: \"Да\" }\n    ],\n    'A2': [\n      { english: \"Family\", russian: \"Семья\" },\n      { english: \"House\", russian: \"Дом\" },\n      { english: \"School\", russian: \"Школа\" },\n      { english: \"Friend\", russian: \"Друг\" },\n      { english: \"Work\", russian: \"Работа\" },\n      { english: \"Food\", russian: \"Еда\" }\n    ],\n    'B1': [\n      { english: \"Experience\", russian: \"Опыт\" },\n      { english: \"Important\", russian: \"Важный\" },\n      { english: \"Difficult\", russian: \"Трудный\" },\n      { english: \"Interesting\", russian: \"Интересный\" },\n      { english: \"Beautiful\", russian: \"Красивый\" },\n      { english: \"Successful\", russian: \"Успешный\" }\n    ]\n  }\n\n\n\n  const handleGameComplete = (correct: boolean, score?: number) => {\n    console.log('Game completed:', { correct, score })\n    const message = `🎉 Отлично! Вы нашли все пары!\\nИтоговый счёт: ${score || 0} очков`\n    alert(message)\n  }\n\n  const resetGame = () => {\n    setGameKey(prev => prev + 1)\n  }\n\n  const changeLevel = (level: string) => {\n    setSelectedLevel(level)\n    setGameKey(prev => prev + 1)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4\">\n      <div className=\"max-w-md mx-auto\">\n        {/* Header */}\n        <div className=\"flex items-center gap-4 mb-6\">\n          <Link\n            href=\"/\"\n            className=\"flex items-center justify-center w-10 h-10 rounded-full bg-white shadow-sm hover:shadow-md transition-shadow\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </Link>\n          <h1 className=\"text-xl font-semibold text-gray-900\">\n            Тест игры Memory Match\n          </h1>\n        </div>\n\n        {/* Level Selector */}\n        <Card className=\"mb-4\">\n          <CardContent className=\"p-4\">\n            <h3 className=\"font-medium text-gray-800 mb-3\">Выберите уровень:</h3>\n            <div className=\"flex gap-2\">\n              {Object.keys(wordPairsByLevel).map((level) => (\n                <Button\n                  key={level}\n                  variant={selectedLevel === level ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => changeLevel(level)}\n                  className=\"flex-1\"\n                >\n                  {level}\n                </Button>\n              ))}\n            </div>\n\n          </CardContent>\n        </Card>\n\n        {/* Game Controls */}\n        <div className=\"flex justify-center mb-4\">\n          <Button\n            variant=\"outline\"\n            onClick={resetGame}\n            className=\"flex items-center gap-2\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n            Перезапустить игру\n          </Button>\n        </div>\n\n        {/* Game Card */}\n        <Card className=\"shadow-lg\">\n          <CardHeader className=\"text-center pb-4\">\n            <h2 className=\"text-lg font-medium text-gray-800\">\n              Уровень {selectedLevel}\n            </h2>\n          </CardHeader>\n          <CardContent>\n            <MemoryMatchGame\n              key={gameKey}\n              wordPairs={wordPairsByLevel[selectedLevel as keyof typeof wordPairsByLevel]}\n              onComplete={handleGameComplete}\n            />\n          </CardContent>\n        </Card>\n\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,mBAAmB;QACvB,MAAM;YACJ;gBAAE,SAAS;gBAAS,SAAS;YAAS;YACtC;gBAAE,SAAS;gBAAgB,SAAS;YAAc;YAClD;gBAAE,SAAS;gBAAa,SAAS;YAAU;YAC3C;gBAAE,SAAS;gBAAU,SAAS;YAAa;YAC3C;gBAAE,SAAS;gBAAW,SAAS;YAAc;YAC7C;gBAAE,SAAS;gBAAO,SAAS;YAAK;SACjC;QACD,MAAM;YACJ;gBAAE,SAAS;gBAAU,SAAS;YAAQ;YACtC;gBAAE,SAAS;gBAAS,SAAS;YAAM;YACnC;gBAAE,SAAS;gBAAU,SAAS;YAAQ;YACtC;gBAAE,SAAS;gBAAU,SAAS;YAAO;YACrC;gBAAE,SAAS;gBAAQ,SAAS;YAAS;YACrC;gBAAE,SAAS;gBAAQ,SAAS;YAAM;SACnC;QACD,MAAM;YACJ;gBAAE,SAAS;gBAAc,SAAS;YAAO;YACzC;gBAAE,SAAS;gBAAa,SAAS;YAAS;YAC1C;gBAAE,SAAS;gBAAa,SAAS;YAAU;YAC3C;gBAAE,SAAS;gBAAe,SAAS;YAAa;YAChD;gBAAE,SAAS;gBAAa,SAAS;YAAW;YAC5C;gBAAE,SAAS;gBAAc,SAAS;YAAW;SAC9C;IACH;IAIA,MAAM,qBAAqB,CAAC,SAAkB;QAC5C,QAAQ,GAAG,CAAC,mBAAmB;YAAE;YAAS;QAAM;QAChD,MAAM,UAAU,CAAC,+CAA+C,EAAE,SAAS,EAAE,MAAM,CAAC;QACpF,MAAM;IACR;IAEA,MAAM,YAAY;QAChB,WAAW,CAAA,OAAQ,OAAO;IAC5B;IAEA,MAAM,cAAc,CAAC;QACnB,iBAAiB;QACjB,WAAW,CAAA,OAAQ,OAAO;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAMtD,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,sBAClC,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,kBAAkB,QAAQ,YAAY;wCAC/C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAET;uCANI;;;;;;;;;;;;;;;;;;;;;8BAef,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAMrC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC;gCAAG,WAAU;;oCAAoC;oCACvC;;;;;;;;;;;;sCAGb,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,kJAAA,CAAA,kBAAe;gCAEd,WAAW,gBAAgB,CAAC,cAA+C;gCAC3E,YAAY;+BAFP;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}