'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function QuickTestPage() {
  const router = useRouter()
  const [telegramId, setTelegramId] = useState<string>('')
  const [apiStatus, setApiStatus] = useState<string>('Не проверено')
  const [userExists, setUserExists] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // Получаем текущий telegram_id
    const currentId = localStorage.getItem('telegram_user_id') || 'Не установлен'
    setTelegramId(currentId)
  }, [])

  const testAPI = async () => {
    setLoading(true)
    setApiStatus('Проверяем...')
    
    try {
      const response = await fetch(`/api/users?telegram_id=${telegramId}`)
      const data = await response.json()
      
      if (response.ok) {
        setApiStatus(`✅ API работает (${response.status})`)
        setUserExists(!!data.user)
      } else {
        setApiStatus(`❌ API ошибка (${response.status}): ${data.error || 'Неизвестная ошибка'}`)
        setUserExists(null)
      }
    } catch (error) {
      setApiStatus(`💥 Ошибка сети: ${error}`)
      setUserExists(null)
    } finally {
      setLoading(false)
    }
  }

  const createNewId = () => {
    const newId = Date.now().toString()
    localStorage.setItem('telegram_user_id', newId)
    setTelegramId(newId)
    setApiStatus('Не проверено')
    setUserExists(null)
  }

  const clearAll = () => {
    localStorage.clear()
    setTelegramId('Не установлен')
    setApiStatus('Не проверено')
    setUserExists(null)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            🧪 Быстрый тест системы
          </h1>

          {/* Telegram ID */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="font-semibold text-blue-900 mb-2">Telegram ID</h2>
            <p className="text-blue-800 font-mono text-sm break-all">
              {telegramId}
            </p>
            <div className="mt-3 space-x-2">
              <button
                onClick={createNewId}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                🆕 Новый ID
              </button>
              <button
                onClick={clearAll}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                🗑️ Очистить все
              </button>
            </div>
          </div>

          {/* API тест */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg">
            <h2 className="font-semibold text-green-900 mb-2">API тест</h2>
            <p className="text-green-800 text-sm mb-3">
              Статус: {apiStatus}
            </p>
            {userExists !== null && (
              <p className="text-green-800 text-sm mb-3">
                Пользователь: {userExists ? '✅ Существует' : '❌ Не найден'}
              </p>
            )}
            <button
              onClick={testAPI}
              disabled={loading || telegramId === 'Не установлен'}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
            >
              {loading ? '⏳ Проверяем...' : '🔍 Проверить API'}
            </button>
          </div>

          {/* Навигация */}
          <div className="mb-6 p-4 bg-purple-50 rounded-lg">
            <h2 className="font-semibold text-purple-900 mb-3">Навигация</h2>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => router.push('/')}
                className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              >
                🏠 Главная
              </button>
              <button
                onClick={() => router.push('/welcome')}
                className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              >
                👋 Приветствие
              </button>
              <button
                onClick={() => router.push('/admin')}
                className="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              >
                ⚙️ Админ
              </button>
              <button
                onClick={() => router.push('/emergency-reset')}
                className="px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                🚨 Сброс
              </button>
            </div>
          </div>

          {/* Системная информация */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h2 className="font-semibold text-gray-900 mb-3">Системная информация</h2>
            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>URL:</strong> {window.location.href}</p>
              <p><strong>User Agent:</strong> {navigator.userAgent}</p>
              <p><strong>Время:</strong> {new Date().toLocaleString()}</p>
              <p><strong>localStorage размер:</strong> {Object.keys(localStorage).length} ключей</p>
              <p><strong>Доступные ключи:</strong> {Object.keys(localStorage).join(', ') || 'Нет'}</p>
            </div>
          </div>

          {/* Действия */}
          <div className="mt-6 flex space-x-2">
            <button
              onClick={() => window.location.reload()}
              className="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              🔄 Обновить страницу
            </button>
            <button
              onClick={() => window.history.back()}
              className="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ← Назад
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
