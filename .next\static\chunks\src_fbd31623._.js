(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/telegram.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// For now, we'll use the global Telegram WebApp object
// import { initData, miniApp } from '@telegram-apps/sdk'
__turbopack_context__.s({
    "closeTelegramWebApp": (()=>closeTelegramWebApp),
    "getTelegramUser": (()=>getTelegramUser),
    "getTelegramWebApp": (()=>getTelegramWebApp),
    "initTelegramWebApp": (()=>initTelegramWebApp),
    "isTelegramWebApp": (()=>isTelegramWebApp),
    "showTelegramAlert": (()=>showTelegramAlert),
    "showTelegramConfirm": (()=>showTelegramConfirm),
    "triggerHapticFeedback": (()=>triggerHapticFeedback),
    "validateTelegramData": (()=>validateTelegramData)
});
function initTelegramWebApp() {
    if ("TURBOPACK compile-time truthy", 1) {
        try {
            const webApp = window.Telegram?.WebApp;
            if (webApp) {
                webApp.ready();
                webApp.expand();
                webApp.enableClosingConfirmation();
                // Set theme colors
                webApp.setHeaderColor('#1f2937') // gray-800
                ;
                webApp.setBackgroundColor('#f9fafb') // gray-50
                ;
                return true;
            }
            return false;
        } catch (error) {
            console.error('Failed to initialize Telegram WebApp:', error);
            return false;
        }
    }
    return false;
}
function getTelegramUser() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        // Check if we're in Telegram environment
        if ("object" !== 'undefined' && window.Telegram?.WebApp?.initDataUnsafe?.user) {
            console.warn('Using Telegram WebApp data');
            return window.Telegram.WebApp.initDataUnsafe.user;
        }
        // For development, return a mock user
        console.warn('Using mock user for development');
        return {
            id: 123456789,
            first_name: 'Test',
            last_name: 'User',
            username: 'testuser'
        };
    } catch (error) {
        console.error('Failed to get Telegram user data:', error);
        // Return mock user as fallback
        return {
            id: 123456789,
            first_name: 'Test',
            last_name: 'User',
            username: 'testuser'
        };
    }
}
function validateTelegramData(initData, botToken) {
    if (!initData || !botToken) return false;
    try {
        const urlParams = new URLSearchParams(initData);
        const hash = urlParams.get('hash');
        urlParams.delete('hash');
        const dataCheckString = Array.from(urlParams.entries()).sort(([a], [b])=>a.localeCompare(b)).map(([key, value])=>`${key}=${value}`).join('\n');
        const crypto = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/crypto-browserify/index.js [app-client] (ecmascript)");
        const secretKey = crypto.createHmac('sha256', 'WebAppData').update(botToken).digest();
        const calculatedHash = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');
        return calculatedHash === hash;
    } catch (error) {
        console.error('Error validating Telegram data:', error);
        return false;
    }
}
function isTelegramWebApp() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return !!(window.Telegram?.WebApp || window.location.search.includes('tgWebAppData') || window.location.hash.includes('tgWebAppData'));
}
function getTelegramWebApp() {
    if ("object" !== 'undefined' && window.Telegram?.WebApp) {
        return window.Telegram.WebApp;
    }
    return null;
}
function showTelegramAlert(message) {
    try {
        const webApp = window.Telegram?.WebApp;
        if (webApp) {
            webApp.showAlert(message);
        } else {
            alert(message);
        }
    } catch (error) {
        alert(message);
    }
}
function showTelegramConfirm(message, callback) {
    try {
        const webApp = window.Telegram?.WebApp;
        if (webApp) {
            webApp.showConfirm(message, callback);
        } else {
            const confirmed = confirm(message);
            callback(confirmed);
        }
    } catch (error) {
        const confirmed = confirm(message);
        callback(confirmed);
    }
}
function triggerHapticFeedback(type = 'light') {
    try {
        // Use the miniApp haptic feedback if available
        if ("object" !== 'undefined' && window.Telegram?.WebApp?.HapticFeedback) {
            const haptic = window.Telegram.WebApp.HapticFeedback;
            switch(type){
                case 'light':
                    haptic.impactOccurred('light');
                    break;
                case 'medium':
                    haptic.impactOccurred('medium');
                    break;
                case 'heavy':
                    haptic.impactOccurred('heavy');
                    break;
                case 'rigid':
                    haptic.impactOccurred('rigid');
                    break;
                case 'soft':
                    haptic.impactOccurred('soft');
                    break;
            }
        }
    } catch (error) {
        // Silently fail if haptic feedback is not available
        console.debug('Haptic feedback not available:', error);
    }
}
function closeTelegramWebApp() {
    try {
        const webApp = window.Telegram?.WebApp;
        if (webApp) {
            webApp.close();
        }
    } catch (error) {
        console.error('Failed to close Telegram WebApp:', error);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "isSupabaseConfigured": (()=>isSupabaseConfigured),
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://qhfixfwfhjqxeqzfmdeg.supabase.co") || 'https://placeholder.supabase.co';
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFoZml4ZndmaGpxeGVxemZtZGVnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwODExNTEsImV4cCI6MjA2NzY1NzE1MX0.-VsfFbHIUZYng2t7uq5awAbZAGE87Pzo45nMZjW4POw") || 'placeholder_key';
// Check if we have valid Supabase configuration
const isSupabaseConfigured = supabaseUrl !== 'https://placeholder.supabase.co' && supabaseAnonKey !== 'placeholder_key' && !supabaseUrl.includes('placeholder') && !supabaseAnonKey.includes('placeholder');
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder_service_key');
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/database.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateLevel": (()=>calculateLevel),
    "createUser": (()=>createUser),
    "getAllLessons": (()=>getAllLessons),
    "getExercisesByLessonId": (()=>getExercisesByLessonId),
    "getFlashcardsByLevel": (()=>getFlashcardsByLevel),
    "getLessonsByLevel": (()=>getLessonsByLevel),
    "getUserByTelegramId": (()=>getUserByTelegramId),
    "getUserProgress": (()=>getUserProgress),
    "getXPForNextLevel": (()=>getXPForNextLevel),
    "markExerciseComplete": (()=>markExerciseComplete),
    "updateUserXP": (()=>updateUserXP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
async function createUser(userData) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupabaseConfigured"]) {
        console.warn('Supabase not configured, returning mock user');
        return {
            id: 'mock-user-id',
            telegram_id: userData.telegram_id,
            username: userData.username,
            first_name: userData.first_name,
            last_name: userData.last_name,
            level: userData.level || 'A1',
            xp: userData.xp || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
    }
    try {
        // Use the admin client to bypass RLS for user creation
        const { supabaseAdmin } = await __turbopack_context__.r("[project]/src/lib/supabase.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const { data, error } = await supabaseAdmin.from('users').insert(userData).select().single();
        if (error) {
            console.error('Error creating user:', error);
            // Return a mock user if creation fails
            return {
                id: `user-${userData.telegram_id}`,
                telegram_id: userData.telegram_id,
                username: userData.username,
                first_name: userData.first_name,
                last_name: userData.last_name,
                level: userData.level || 'A1',
                xp: userData.xp || 0,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
        }
        return data;
    } catch (error) {
        console.error('Error in createUser:', error);
        // Return a mock user as fallback
        return {
            id: `user-${userData.telegram_id}`,
            telegram_id: userData.telegram_id,
            username: userData.username,
            first_name: userData.first_name,
            last_name: userData.last_name,
            level: userData.level || 'A1',
            xp: userData.xp || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
    }
}
async function getUserByTelegramId(telegramId) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupabaseConfigured"]) {
        console.warn('Supabase not configured, returning null (user will be created)');
        return null;
    }
    try {
        // Use admin client to bypass RLS issues
        const { supabaseAdmin } = await __turbopack_context__.r("[project]/src/lib/supabase.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const { data, error } = await supabaseAdmin.from('users').select('*').eq('telegram_id', telegramId).single();
        if (error) {
            console.error('Error fetching user:', error);
            return null;
        }
        return data;
    } catch (error) {
        console.error('Error in getUserByTelegramId:', error);
        return null;
    }
}
async function updateUserXP(userId, xpToAdd) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupabaseConfigured"]) {
        console.warn('Supabase not configured, XP update skipped');
        return null;
    }
    try {
        // Use admin client to bypass RLS issues
        const { supabaseAdmin } = await __turbopack_context__.r("[project]/src/lib/supabase.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const { data, error } = await supabaseAdmin.from('users').update({
            xp: supabaseAdmin.sql`xp + ${xpToAdd}`
        }).eq('id', userId).select().single();
        if (error) {
            console.error('Error updating user XP:', error);
            return null;
        }
        return data;
    } catch (error) {
        console.error('Error in updateUserXP:', error);
        return null;
    }
}
async function getLessonsByLevel(level) {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('lessons').select('*').eq('level', level).order('order');
    if (error) {
        console.error('Error fetching lessons:', error);
        return [];
    }
    return data || [];
}
async function getAllLessons() {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupabaseConfigured"]) {
        console.warn('Supabase not configured, returning mock lessons');
        return [
            {
                id: 'lesson-1',
                title: 'Basic Greetings',
                description: 'Learn how to say hello and introduce yourself',
                level: 'A1',
                order: 1,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 'lesson-2',
                title: 'Numbers 1-10',
                description: 'Learn basic numbers from one to ten',
                level: 'A1',
                order: 2,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 'lesson-3',
                title: 'Present Simple Tense',
                description: 'Learn how to use present simple tense',
                level: 'A2',
                order: 1,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];
    }
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('lessons').select('*').order('level, order');
    if (error) {
        console.error('Error fetching all lessons:', error);
        return [];
    }
    return data || [];
}
async function getExercisesByLessonId(lessonId) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupabaseConfigured"]) {
        console.warn('Supabase not configured, returning mock exercises');
        return [
            {
                id: 'exercise-1',
                lesson_id: lessonId,
                type: 'quiz',
                content_json: {
                    question: "How do you greet someone in the morning?",
                    options: [
                        "Good morning",
                        "Good night",
                        "Good afternoon",
                        "Good evening"
                    ],
                    correct: "Good morning"
                },
                xp_reward: 10,
                order: 1,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 'exercise-2',
                lesson_id: lessonId,
                type: 'fill-in-the-blank',
                content_json: {
                    sentence: "Hello, my name ___ John.",
                    options: [
                        "is",
                        "are",
                        "am",
                        "be"
                    ],
                    correct: "is"
                },
                xp_reward: 10,
                order: 2,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 'exercise-3',
                lesson_id: lessonId,
                type: 'sentence-builder',
                content_json: {
                    translation: "Меня зовут Анна.",
                    correct_order: [
                        "My",
                        "name",
                        "is",
                        "Anna"
                    ],
                    extra_words: [
                        "am",
                        "called",
                        "the"
                    ]
                },
                xp_reward: 15,
                order: 3,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];
    }
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('exercises').select('*').eq('lesson_id', lessonId).order('order');
    if (error) {
        console.error('Error fetching exercises:', error);
        return [];
    }
    return data || [];
}
async function getUserProgress(userId) {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_progress').select('*').eq('user_id', userId);
    if (error) {
        console.error('Error fetching user progress:', error);
        return [];
    }
    return data || [];
}
async function markExerciseComplete(userId, lessonId, exerciseId, score) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupabaseConfigured"]) {
        console.warn('Supabase not configured, progress not saved');
        return null;
    }
    try {
        // Use admin client to bypass RLS issues
        const { supabaseAdmin } = await __turbopack_context__.r("[project]/src/lib/supabase.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const { data, error } = await supabaseAdmin.from('user_progress').upsert({
            user_id: userId,
            lesson_id: lessonId,
            exercise_id: exerciseId,
            completed: true,
            score,
            completed_at: new Date().toISOString()
        }).select().single();
        if (error) {
            console.error('Error marking exercise complete:', error);
            return null;
        }
        return data;
    } catch (error) {
        console.error('Error in markExerciseComplete:', error);
        return null;
    }
}
async function getFlashcardsByLevel(level) {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('flashcards').select('*').eq('level', level);
    if (error) {
        console.error('Error fetching flashcards:', error);
        return [];
    }
    return data || [];
}
function calculateLevel(xp) {
    if (xp < 100) return 'A1';
    if (xp < 300) return 'A2';
    if (xp < 600) return 'B1';
    if (xp < 1000) return 'B2';
    if (xp < 1500) return 'C1';
    return 'C2';
}
function getXPForNextLevel(currentLevel) {
    const levelThresholds = {
        'A1': 100,
        'A2': 300,
        'B1': 600,
        'B2': 1000,
        'C1': 1500,
        'C2': 2000
    };
    return levelThresholds[currentLevel] || 2000;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useUser": (()=>useUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$telegram$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/telegram.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [telegramUser, setTelegramUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            initializeAuth();
        }
    }["AuthProvider.useEffect"], []);
    const initializeAuth = async ()=>{
        try {
            setLoading(true);
            setError(null);
            // Initialize Telegram WebApp
            const telegramInitialized = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$telegram$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initTelegramWebApp"])();
            if (!telegramInitialized) {
                console.warn('Telegram WebApp not initialized - running in development mode');
            }
            // Get Telegram user data
            const tgUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$telegram$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTelegramUser"])();
            if (!tgUser) {
                // For development, create a mock user
                const mockUser = {
                    id: 123456789,
                    first_name: 'Test',
                    last_name: 'User',
                    username: 'testuser'
                };
                setTelegramUser(mockUser);
                await handleUserLogin(mockUser);
                return;
            }
            setTelegramUser(tgUser);
            await handleUserLogin(tgUser);
        } catch (err) {
            console.error('Auth initialization error:', err);
            setError('Failed to initialize authentication');
        } finally{
            setLoading(false);
        }
    };
    const handleUserLogin = async (tgUser)=>{
        try {
            // Check if user exists in database
            let dbUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUserByTelegramId"])(tgUser.id);
            if (!dbUser) {
                // For new users, we'll create them automatically
                // In a real app, you might want to redirect to registration page
                dbUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createUser"])({
                    telegram_id: tgUser.id,
                    username: tgUser.username || null,
                    first_name: tgUser.first_name,
                    last_name: tgUser.last_name || null,
                    level: 'A1',
                    xp: 0
                });
            }
            if (dbUser) {
                setUser(dbUser);
            } else {
                throw new Error('Failed to create or retrieve user');
            }
        } catch (err) {
            console.error('User login error:', err);
            setError('Failed to login user');
        }
    };
    const login = async ()=>{
        await initializeAuth();
    };
    const logout = ()=>{
        setUser(null);
        setTelegramUser(null);
        setError(null);
    };
    const value = {
        user,
        telegramUser,
        loading,
        error,
        login,
        logout
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "KRTJoeePMuoKwOeQ3HakAOe6BuQ=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useUser() {
    _s2();
    const { user, loading, error } = useAuth();
    return {
        user,
        loading,
        error
    };
}
_s2(useUser, "0Oj1uxQ9sPd1TYF7cc1lBwLoQgk=", false, function() {
    return [
        useAuth
    ];
});
function useIsAuthenticated() {
    _s3();
    const { user, loading } = useAuth();
    return {
        isAuthenticated: !!user,
        loading
    };
}
_s3(useIsAuthenticated, "EmJkapf7qiLC5Br5eCoEq4veZes=", false, function() {
    return [
        useAuth
    ];
});
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_fbd31623._.js.map