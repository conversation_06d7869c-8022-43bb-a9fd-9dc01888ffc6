{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/MemoryMatchGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, Clock, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface CardData {\n  id: string\n  text: string\n  type: 'english' | 'russian'\n  pairId: string\n}\n\ninterface MemoryMatchGameProps {\n  wordPairs: Array<{ english: string; russian: string }>\n  timeLimit?: number // в секундах, по умолчанию 120 секунд (2 минуты)\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function MemoryMatchGame({ \n  wordPairs, \n  timeLimit = 120,\n  onComplete \n}: MemoryMatchGameProps) {\n  const [cards, setCards] = useState<CardData[]>([])\n  const [selectedCards, setSelectedCards] = useState<string[]>([])\n  const [matchedPairs, setMatchedPairs] = useState<string[]>([])\n  const [wrongPairs, setWrongPairs] = useState<string[]>([])\n  const [timeLeft, setTimeLeft] = useState(timeLimit)\n  const [gameStarted, setGameStarted] = useState(false)\n  const [gameEnded, setGameEnded] = useState(false)\n  const [score, setScore] = useState(0)\n\n  // Инициализация карточек\n  useEffect(() => {\n    const gameCards: CardData[] = []\n    \n    wordPairs.forEach((pair, index) => {\n      const pairId = `pair-${index}`\n      gameCards.push({\n        id: `en-${index}`,\n        text: pair.english,\n        type: 'english',\n        pairId\n      })\n      gameCards.push({\n        id: `ru-${index}`,\n        text: pair.russian,\n        type: 'russian',\n        pairId\n      })\n    })\n    \n    // Перемешиваем карточки\n    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)\n    setCards(shuffledCards)\n  }, [wordPairs])\n\n  // Таймер\n  useEffect(() => {\n    if (!gameStarted || gameEnded) return\n\n    const timer = setInterval(() => {\n      setTimeLeft((prev) => {\n        if (prev <= 1) {\n          setGameEnded(true)\n          onComplete(false, score)\n          return 0\n        }\n        return prev - 1\n      })\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [gameStarted, gameEnded, score, onComplete])\n\n  // Проверка завершения игры\n  useEffect(() => {\n    if (matchedPairs.length === wordPairs.length && wordPairs.length > 0) {\n      setGameEnded(true)\n      const finalScore = Math.max(0, 100 - Math.floor((timeLimit - timeLeft) / 2))\n      setScore(finalScore)\n      setTimeout(() => {\n        onComplete(true, finalScore)\n      }, 1500)\n    }\n  }, [matchedPairs.length, wordPairs.length, timeLimit, timeLeft, onComplete])\n\n  const startGame = () => {\n    setGameStarted(true)\n    triggerHapticFeedback('light')\n  }\n\n  const resetGame = () => {\n    setSelectedCards([])\n    setMatchedPairs([])\n    setWrongPairs([])\n    setTimeLeft(timeLimit)\n    setGameStarted(false)\n    setGameEnded(false)\n    setScore(0)\n    \n    // Перемешиваем карточки заново\n    const gameCards: CardData[] = []\n    wordPairs.forEach((pair, index) => {\n      const pairId = `pair-${index}`\n      gameCards.push({\n        id: `en-${index}`,\n        text: pair.english,\n        type: 'english',\n        pairId\n      })\n      gameCards.push({\n        id: `ru-${index}`,\n        text: pair.russian,\n        type: 'russian',\n        pairId\n      })\n    })\n    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)\n    setCards(shuffledCards)\n    \n    triggerHapticFeedback('light')\n  }\n\n  const handleCardClick = useCallback((cardId: string) => {\n    if (!gameStarted || gameEnded || selectedCards.includes(cardId) || matchedPairs.some(pairId => \n      cards.find(c => c.id === cardId)?.pairId === pairId\n    )) {\n      return\n    }\n\n    const newSelectedCards = [...selectedCards, cardId]\n    setSelectedCards(newSelectedCards)\n\n    if (newSelectedCards.length === 2) {\n      const [firstCardId, secondCardId] = newSelectedCards\n      const firstCard = cards.find(c => c.id === firstCardId)\n      const secondCard = cards.find(c => c.id === secondCardId)\n\n      if (firstCard && secondCard && firstCard.pairId === secondCard.pairId) {\n        // Правильная пара\n        setMatchedPairs(prev => [...prev, firstCard.pairId])\n        setSelectedCards([])\n        triggerHapticFeedback('light')\n      } else {\n        // Неправильная пара\n        setWrongPairs([firstCardId, secondCardId])\n        triggerHapticFeedback('heavy')\n        \n        setTimeout(() => {\n          setSelectedCards([])\n          setWrongPairs([])\n        }, 1000)\n      }\n    }\n  }, [gameStarted, gameEnded, selectedCards, matchedPairs, cards])\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getCardStyle = (card: CardData) => {\n    const isSelected = selectedCards.includes(card.id)\n    const isMatched = matchedPairs.includes(card.pairId)\n    const isWrong = wrongPairs.includes(card.id)\n    \n    let baseStyle = 'h-20 text-sm font-medium transition-all duration-200 '\n    \n    if (isMatched) {\n      baseStyle += 'bg-green-100 border-green-300 text-green-800 opacity-50'\n    } else if (isWrong) {\n      baseStyle += 'bg-red-100 border-red-300 text-red-800'\n    } else if (isSelected) {\n      baseStyle += 'bg-gray-100 border-gray-400 text-gray-800'\n    } else {\n      baseStyle += 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n    }\n    \n    return baseStyle\n  }\n\n  if (!gameStarted) {\n    return (\n      <div className=\"space-y-6 text-center\">\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Игра на сопоставление слов\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Найдите пары: английские слова и их русские переводы\n          </p>\n          <div className=\"flex items-center justify-center gap-2 text-sm text-gray-500 mb-6\">\n            <Clock className=\"h-4 w-4\" />\n            <span>Время: {formatTime(timeLimit)}</span>\n          </div>\n        </div>\n        \n        <Button onClick={startGame} className=\"px-8 py-3\">\n          Начать игру\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Заголовок и таймер */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">\n          Найдите пары слов\n        </h2>\n        <div className=\"flex items-center gap-4\">\n          <div className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${\n            timeLeft <= 30 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n          }`}>\n            <Clock className=\"h-4 w-4\" />\n            <span>{formatTime(timeLeft)}</span>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={resetGame}\n            className=\"p-2\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Прогресс */}\n      <div className=\"text-center text-sm text-gray-600\">\n        Найдено пар: {matchedPairs.length} из {wordPairs.length}\n      </div>\n\n      {/* Игровое поле */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {cards.map((card) => (\n          <Button\n            key={card.id}\n            variant=\"outline\"\n            className={getCardStyle(card)}\n            onClick={() => handleCardClick(card.id)}\n            disabled={gameEnded || matchedPairs.includes(card.pairId)}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-xs text-gray-500 mb-1\">\n                {card.type === 'english' ? 'EN' : 'RU'}\n              </div>\n              <div>{card.text}</div>\n            </div>\n          </Button>\n        ))}\n      </div>\n\n      {/* Результат игры */}\n      {gameEnded && (\n        <Card className={`${matchedPairs.length === wordPairs.length ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {matchedPairs.length === wordPairs.length ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${\n              matchedPairs.length === wordPairs.length ? 'text-green-800' : 'text-red-800'\n            }`}>\n              {matchedPairs.length === wordPairs.length ? 'Отлично!' : 'Время вышло!'}\n            </h3>\n            <p className={`text-sm ${\n              matchedPairs.length === wordPairs.length ? 'text-green-700' : 'text-red-700'\n            }`}>\n              {matchedPairs.length === wordPairs.length\n                ? `Вы нашли все пары! Счёт: ${score}`\n                : `Найдено пар: ${matchedPairs.length} из ${wordPairs.length}`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAqBO,SAAS,gBAAgB,EAC9B,SAAS,EACT,YAAY,GAAG,EACf,UAAU,EACW;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAwB,EAAE;QAEhC,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO;YAC9B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;YACA,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;QACF;QAEA,wBAAwB;QACxB,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC3D,SAAS;IACX,GAAG;QAAC;KAAU;IAEd,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,WAAW;QAE/B,MAAM,QAAQ,YAAY;YACxB,YAAY,CAAC;gBACX,IAAI,QAAQ,GAAG;oBACb,aAAa;oBACb,WAAW,OAAO;oBAClB,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAa;QAAW;QAAO;KAAW;IAE9C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,KAAK,UAAU,MAAM,IAAI,UAAU,MAAM,GAAG,GAAG;YACpE,aAAa;YACb,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC,YAAY,QAAQ,IAAI;YACzE,SAAS;YACT,WAAW;gBACT,WAAW,MAAM;YACnB,GAAG;QACL;IACF,GAAG;QAAC,aAAa,MAAM;QAAE,UAAU,MAAM;QAAE;QAAW;QAAU;KAAW;IAE3E,MAAM,YAAY;QAChB,eAAe;QACf,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;IACxB;IAEA,MAAM,YAAY;QAChB,iBAAiB,EAAE;QACnB,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,YAAY;QACZ,eAAe;QACf,aAAa;QACb,SAAS;QAET,+BAA+B;QAC/B,MAAM,YAAwB,EAAE;QAChC,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO;YAC9B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;YACA,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;QACF;QACA,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC3D,SAAS;QAET,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;IACxB;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,eAAe,aAAa,cAAc,QAAQ,CAAC,WAAW,aAAa,IAAI,CAAC,CAAA,SACnF,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,WAAW,SAC5C;YACD;QACF;QAEA,MAAM,mBAAmB;eAAI;YAAe;SAAO;QACnD,iBAAiB;QAEjB,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,MAAM,CAAC,aAAa,aAAa,GAAG;YACpC,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC3C,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAE5C,IAAI,aAAa,cAAc,UAAU,MAAM,KAAK,WAAW,MAAM,EAAE;gBACrE,kBAAkB;gBAClB,gBAAgB,CAAA,OAAQ;2BAAI;wBAAM,UAAU,MAAM;qBAAC;gBACnD,iBAAiB,EAAE;gBACnB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;YACxB,OAAO;gBACL,oBAAoB;gBACpB,cAAc;oBAAC;oBAAa;iBAAa;gBACzC,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAEtB,WAAW;oBACT,iBAAiB,EAAE;oBACnB,cAAc,EAAE;gBAClB,GAAG;YACL;QACF;IACF,GAAG;QAAC;QAAa;QAAW;QAAe;QAAc;KAAM;IAE/D,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QACjD,MAAM,YAAY,aAAa,QAAQ,CAAC,KAAK,MAAM;QACnD,MAAM,UAAU,WAAW,QAAQ,CAAC,KAAK,EAAE;QAE3C,IAAI,YAAY;QAEhB,IAAI,WAAW;YACb,aAAa;QACf,OAAO,IAAI,SAAS;YAClB,aAAa;QACf,OAAO,IAAI,YAAY;YACrB,aAAa;QACf,OAAO;YACL,aAAa;QACf;QAEA,OAAO;IACT;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;wCAAK;wCAAQ,WAAW;;;;;;;;;;;;;;;;;;;8BAI7B,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAW,WAAU;8BAAY;;;;;;;;;;;;IAKxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,mEAAmE,EAClF,YAAY,KAAK,4BAA4B,6BAC7C;;kDACA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAM,WAAW;;;;;;;;;;;;0CAEpB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM3B,8OAAC;gBAAI,WAAU;;oBAAoC;oBACnC,aAAa,MAAM;oBAAC;oBAAK,UAAU,MAAM;;;;;;;0BAIzD,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW,aAAa;wBACxB,SAAS,IAAM,gBAAgB,KAAK,EAAE;wBACtC,UAAU,aAAa,aAAa,QAAQ,CAAC,KAAK,MAAM;kCAExD,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,KAAK,YAAY,OAAO;;;;;;8CAEpC,8OAAC;8CAAK,KAAK,IAAI;;;;;;;;;;;;uBAVZ,KAAK,EAAE;;;;;;;;;;YAiBjB,2BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,iCAAiC,4BAA4B;0BAC1H,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,KAAK,UAAU,MAAM,iBACvC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EACjC,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,mBAAmB,gBAC9D;sCACC,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,aAAa;;;;;;sCAE3D,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EACrB,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,mBAAmB,gBAC9D;sCACC,aAAa,MAAM,KAAK,UAAU,MAAM,GACrC,CAAC,yBAAyB,EAAE,OAAO,GACnC,CAAC,aAAa,EAAE,aAAa,MAAM,CAAC,IAAI,EAAE,UAAU,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAQ9E", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/test-memory-match/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { MemoryMatchGame } from '@/components/exercises/MemoryMatchGame'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport Link from 'next/link'\nimport { ArrowLeft, RotateCcw } from 'lucide-react'\n\nexport default function TestMemoryMatchPage() {\n  const [gameKey, setGameKey] = useState(0)\n  const [selectedLevel, setSelectedLevel] = useState('A1')\n\n  const wordPairsByLevel = {\n    'A1': [\n      { english: \"Hello\", russian: \"Привет\" },\n      { english: \"Good morning\", russian: \"Доброе утро\" },\n      { english: \"Thank you\", russian: \"Спасибо\" },\n      { english: \"Please\", russian: \"Пожалуйста\" },\n      { english: \"Goodbye\", russian: \"До свидания\" },\n      { english: \"Yes\", russian: \"Да\" }\n    ],\n    'A2': [\n      { english: \"Family\", russian: \"Семья\" },\n      { english: \"House\", russian: \"Дом\" },\n      { english: \"School\", russian: \"Школа\" },\n      { english: \"Friend\", russian: \"Друг\" },\n      { english: \"Work\", russian: \"Работа\" },\n      { english: \"Food\", russian: \"Еда\" }\n    ],\n    'B1': [\n      { english: \"Experience\", russian: \"Опыт\" },\n      { english: \"Important\", russian: \"Важный\" },\n      { english: \"Difficult\", russian: \"Трудный\" },\n      { english: \"Interesting\", russian: \"Интересный\" },\n      { english: \"Beautiful\", russian: \"Красивый\" },\n      { english: \"Successful\", russian: \"Успешный\" }\n    ]\n  }\n\n  const timeLimitByLevel = {\n    'A1': 150,\n    'A2': 140,\n    'B1': 130\n  }\n\n  const handleGameComplete = (correct: boolean, score?: number) => {\n    console.log('Game completed:', { correct, score })\n    const message = correct\n      ? `🎉 Отлично! Вы нашли все пары!\\nСчёт: ${score || 0} очков`\n      : `⏰ Время вышло!\\nПопробуйте еще раз!`\n    alert(message)\n  }\n\n  const resetGame = () => {\n    setGameKey(prev => prev + 1)\n  }\n\n  const changeLevel = (level: string) => {\n    setSelectedLevel(level)\n    setGameKey(prev => prev + 1)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4\">\n      <div className=\"max-w-md mx-auto\">\n        {/* Header */}\n        <div className=\"flex items-center gap-4 mb-6\">\n          <Link\n            href=\"/\"\n            className=\"flex items-center justify-center w-10 h-10 rounded-full bg-white shadow-sm hover:shadow-md transition-shadow\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </Link>\n          <h1 className=\"text-xl font-semibold text-gray-900\">\n            Тест игры Memory Match\n          </h1>\n        </div>\n\n        {/* Level Selector */}\n        <Card className=\"mb-4\">\n          <CardContent className=\"p-4\">\n            <h3 className=\"font-medium text-gray-800 mb-3\">Выберите уровень:</h3>\n            <div className=\"flex gap-2\">\n              {Object.keys(wordPairsByLevel).map((level) => (\n                <Button\n                  key={level}\n                  variant={selectedLevel === level ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => changeLevel(level)}\n                  className=\"flex-1\"\n                >\n                  {level}\n                </Button>\n              ))}\n            </div>\n            <div className=\"mt-2 text-sm text-gray-600 text-center\">\n              Время: {timeLimitByLevel[selectedLevel as keyof typeof timeLimitByLevel]} сек\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Game Controls */}\n        <div className=\"flex justify-center mb-4\">\n          <Button\n            variant=\"outline\"\n            onClick={resetGame}\n            className=\"flex items-center gap-2\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n            Перезапустить игру\n          </Button>\n        </div>\n\n        {/* Game Card */}\n        <Card className=\"shadow-lg\">\n          <CardHeader className=\"text-center pb-4\">\n            <h2 className=\"text-lg font-medium text-gray-800\">\n              Игра на сопоставление слов - {selectedLevel}\n            </h2>\n            <p className=\"text-sm text-gray-600\">\n              Найдите пары английских и русских слов\n            </p>\n          </CardHeader>\n          <CardContent>\n            <MemoryMatchGame\n              key={gameKey}\n              wordPairs={wordPairsByLevel[selectedLevel as keyof typeof wordPairsByLevel]}\n              timeLimit={timeLimitByLevel[selectedLevel as keyof typeof timeLimitByLevel]}\n              onComplete={handleGameComplete}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Instructions */}\n        <Card className=\"mt-6 bg-blue-50 border-blue-200\">\n          <CardContent className=\"p-4\">\n            <h3 className=\"font-medium text-blue-900 mb-2\">Как играть:</h3>\n            <ul className=\"text-sm text-blue-800 space-y-1\">\n              <li>• Нажмите \"Начать игру\" для старта</li>\n              <li>• Выберите карточку с английским словом</li>\n              <li>• Затем выберите соответствующий русский перевод</li>\n              <li>• Правильные пары станут зелеными и исчезнут</li>\n              <li>• Неправильные пары станут красными</li>\n              <li>• Найдите все пары до окончания времени!</li>\n            </ul>\n            <div className=\"mt-3 pt-3 border-t border-blue-200\">\n              <h4 className=\"font-medium text-blue-900 mb-1\">Уровни сложности:</h4>\n              <div className=\"text-xs text-blue-700 space-y-1\">\n                <div>• A1: Базовые слова (150 сек)</div>\n                <div>• A2: Повседневные слова (140 сек)</div>\n                <div>• B1: Сложные слова (130 сек)</div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,mBAAmB;QACvB,MAAM;YACJ;gBAAE,SAAS;gBAAS,SAAS;YAAS;YACtC;gBAAE,SAAS;gBAAgB,SAAS;YAAc;YAClD;gBAAE,SAAS;gBAAa,SAAS;YAAU;YAC3C;gBAAE,SAAS;gBAAU,SAAS;YAAa;YAC3C;gBAAE,SAAS;gBAAW,SAAS;YAAc;YAC7C;gBAAE,SAAS;gBAAO,SAAS;YAAK;SACjC;QACD,MAAM;YACJ;gBAAE,SAAS;gBAAU,SAAS;YAAQ;YACtC;gBAAE,SAAS;gBAAS,SAAS;YAAM;YACnC;gBAAE,SAAS;gBAAU,SAAS;YAAQ;YACtC;gBAAE,SAAS;gBAAU,SAAS;YAAO;YACrC;gBAAE,SAAS;gBAAQ,SAAS;YAAS;YACrC;gBAAE,SAAS;gBAAQ,SAAS;YAAM;SACnC;QACD,MAAM;YACJ;gBAAE,SAAS;gBAAc,SAAS;YAAO;YACzC;gBAAE,SAAS;gBAAa,SAAS;YAAS;YAC1C;gBAAE,SAAS;gBAAa,SAAS;YAAU;YAC3C;gBAAE,SAAS;gBAAe,SAAS;YAAa;YAChD;gBAAE,SAAS;gBAAa,SAAS;YAAW;YAC5C;gBAAE,SAAS;gBAAc,SAAS;YAAW;SAC9C;IACH;IAEA,MAAM,mBAAmB;QACvB,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,MAAM,qBAAqB,CAAC,SAAkB;QAC5C,QAAQ,GAAG,CAAC,mBAAmB;YAAE;YAAS;QAAM;QAChD,MAAM,UAAU,UACZ,CAAC,sCAAsC,EAAE,SAAS,EAAE,MAAM,CAAC,GAC3D,CAAC,mCAAmC,CAAC;QACzC,MAAM;IACR;IAEA,MAAM,YAAY;QAChB,WAAW,CAAA,OAAQ,OAAO;IAC5B;IAEA,MAAM,cAAc,CAAC;QACnB,iBAAiB;QACjB,WAAW,CAAA,OAAQ,OAAO;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAMtD,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,sBAClC,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,kBAAkB,QAAQ,YAAY;wCAC/C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAET;uCANI;;;;;;;;;;0CAUX,8OAAC;gCAAI,WAAU;;oCAAyC;oCAC9C,gBAAgB,CAAC,cAA+C;oCAAC;;;;;;;;;;;;;;;;;;8BAM/E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAMrC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC;oCAAG,WAAU;;wCAAoC;wCAClB;;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,kJAAA,CAAA,kBAAe;gCAEd,WAAW,gBAAgB,CAAC,cAA+C;gCAC3E,WAAW,gBAAgB,CAAC,cAA+C;gCAC3E,YAAY;+BAHP;;;;;;;;;;;;;;;;8BASX,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;0CAEN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}]}