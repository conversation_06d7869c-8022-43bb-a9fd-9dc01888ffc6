{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA7BgB", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/ProgressBar.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ProgressBarProps {\n  value: number\n  max: number\n  className?: string\n  showLabel?: boolean\n  label?: string\n}\n\nexport function ProgressBar({ \n  value, \n  max, \n  className, \n  showLabel = false, \n  label \n}: ProgressBarProps) {\n  const percentage = Math.min((value / max) * 100, 100)\n  \n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>{label}</span>\n          <span>{value}/{max}</span>\n        </div>\n      )}\n      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n        <div\n          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,SAAS,YAAY,EAC1B,KAAK,EACL,GAAG,EACH,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACY;IACjB,MAAM,aAAa,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK;IAEjD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM;;;;;;kCACP,6LAAC;;4BAAM;4BAAM;4BAAE;;;;;;;;;;;;;0BAGnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C;KAzBgB", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR;KAdgB", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;KAbgB;AAeT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/QuizGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface QuizGameProps {\n  question: string\n  options: string[]\n  correct: string\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function QuizGame({ question, options, correct, onComplete }: QuizGameProps) {\n  const [selectedOption, setSelectedOption] = useState<string | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleOptionSelect = (option: string) => {\n    if (showResult) return\n    \n    setSelectedOption(option)\n    const correct_answer = option === correct\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 1500)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Question */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Вопрос викторины\n        </h2>\n        <p className=\"text-lg text-gray-700\">\n          {question}\n        </p>\n      </div>\n\n      {/* Options */}\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          let buttonClass = 'w-full text-left justify-start h-auto py-4 px-4'\n          let icon = null\n\n          if (showResult) {\n            if (option === correct) {\n              buttonClass += ' bg-green-100 border-green-300 text-green-800'\n              icon = <CheckCircle className=\"h-5 w-5 text-green-600\" />\n            } else if (option === selectedOption && option !== correct) {\n              buttonClass += ' bg-red-100 border-red-300 text-red-800'\n              icon = <XCircle className=\"h-5 w-5 text-red-600\" />\n            } else {\n              buttonClass += ' opacity-50'\n            }\n          } else {\n            buttonClass += ' hover:bg-gray-50 border-gray-300'\n          }\n\n          return (\n            <Button\n              key={index}\n              variant=\"outline\"\n              className={buttonClass}\n              onClick={() => handleOptionSelect(option)}\n              disabled={showResult}\n            >\n              <div className=\"flex items-center justify-between w-full\">\n                <span className=\"text-base\">{option}</span>\n                {icon}\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Отлично! Вы ответили правильно.'\n                : `Правильный ответ: \"${correct}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAeO,SAAS,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAiB;;IAChF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAEhB,kBAAkB;QAClB,MAAM,iBAAiB,WAAW;QAClC,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;oBACpB,IAAI,cAAc;oBAClB,IAAI,OAAO;oBAEX,IAAI,YAAY;wBACd,IAAI,WAAW,SAAS;4BACtB,eAAe;4BACf,qBAAO,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;wBAChC,OAAO,IAAI,WAAW,kBAAkB,WAAW,SAAS;4BAC1D,eAAe;4BACf,qBAAO,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;wBAC5B,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO;wBACL,eAAe;oBACjB;oBAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW;wBACX,SAAS,IAAM,mBAAmB;wBAClC,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAa;;;;;;gCAC5B;;;;;;;uBARE;;;;;gBAYX;;;;;;YAID,4BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,oCACA,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GAhGgB;KAAA", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/FillInTheBlankGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface FillInTheBlankGameProps {\n  sentence: string\n  options: string[]\n  correct: string\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function FillInTheBlankGame({ \n  sentence, \n  options, \n  correct, \n  onComplete \n}: FillInTheBlankGameProps) {\n  const [selectedOption, setSelectedOption] = useState<string | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleOptionSelect = (option: string) => {\n    if (showResult) return\n    \n    setSelectedOption(option)\n    const correct_answer = option === correct\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 1500)\n  }\n\n  // Split sentence by blank (represented by ___)\n  const renderSentence = () => {\n    const parts = sentence.split('___')\n    if (parts.length !== 2) {\n      // Fallback if sentence doesn't have exactly one blank\n      return (\n        <span className=\"text-lg\">\n          {sentence.replace('___', `[${selectedOption || '___'}]`)}\n        </span>\n      )\n    }\n\n    return (\n      <span className=\"text-lg\">\n        {parts[0]}\n        <span className={`inline-block min-w-[80px] px-2 py-1 mx-1 rounded border-2 border-dashed text-center font-medium ${\n          showResult\n            ? isCorrect\n              ? 'bg-green-100 border-green-300 text-green-800'\n              : 'bg-red-100 border-red-300 text-red-800'\n            : selectedOption\n              ? 'bg-blue-100 border-blue-300 text-blue-800'\n              : 'bg-gray-100 border-gray-300 text-gray-500'\n        }`}>\n          {selectedOption || '___'}\n        </span>\n        {parts[1]}\n      </span>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Заполните пропуск\n        </h2>\n        <p className=\"text-sm text-gray-600\">\n          Выберите правильное слово для завершения предложения\n        </p>\n      </div>\n\n      {/* Sentence with blank */}\n      <Card className=\"bg-gray-50\">\n        <CardContent className=\"p-6 text-center\">\n          {renderSentence()}\n        </CardContent>\n      </Card>\n\n      {/* Options */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {options.map((option, index) => {\n          let buttonClass = 'h-12'\n          let icon = null\n\n          if (showResult) {\n            if (option === correct) {\n              buttonClass += ' bg-green-100 border-green-300 text-green-800'\n              icon = <CheckCircle className=\"h-4 w-4 text-green-600 ml-2\" />\n            } else if (option === selectedOption && option !== correct) {\n              buttonClass += ' bg-red-100 border-red-300 text-red-800'\n              icon = <XCircle className=\"h-4 w-4 text-red-600 ml-2\" />\n            } else {\n              buttonClass += ' opacity-50'\n            }\n          } else if (option === selectedOption) {\n            buttonClass += ' bg-blue-100 border-blue-300 text-blue-800'\n          }\n\n          return (\n            <Button\n              key={index}\n              variant=\"outline\"\n              className={buttonClass}\n              onClick={() => handleOptionSelect(option)}\n              disabled={showResult}\n            >\n              <div className=\"flex items-center justify-center\">\n                <span>{option}</span>\n                {icon}\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Отлично! Вы правильно заполнили пропуск.'\n                : `Правильный ответ: \"${correct}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAeO,SAAS,mBAAmB,EACjC,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACc;;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAEhB,kBAAkB;QAClB,MAAM,iBAAiB,WAAW;QAClC,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,+CAA+C;IAC/C,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,sDAAsD;YACtD,qBACE,6LAAC;gBAAK,WAAU;0BACb,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,kBAAkB,MAAM,CAAC,CAAC;;;;;;QAG7D;QAEA,qBACE,6LAAC;YAAK,WAAU;;gBACb,KAAK,CAAC,EAAE;8BACT,6LAAC;oBAAK,WAAW,CAAC,gGAAgG,EAChH,aACI,YACE,iDACA,2CACF,iBACE,8CACA,6CACN;8BACC,kBAAkB;;;;;;gBAEpB,KAAK,CAAC,EAAE;;;;;;;IAGf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;oBACpB,IAAI,cAAc;oBAClB,IAAI,OAAO;oBAEX,IAAI,YAAY;wBACd,IAAI,WAAW,SAAS;4BACtB,eAAe;4BACf,qBAAO,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;wBAChC,OAAO,IAAI,WAAW,kBAAkB,WAAW,SAAS;4BAC1D,eAAe;4BACf,qBAAO,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;wBAC5B,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO,IAAI,WAAW,gBAAgB;wBACpC,eAAe;oBACjB;oBAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW;wBACX,SAAS,IAAM,mBAAmB;wBAClC,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAM;;;;;;gCACN;;;;;;;uBARE;;;;;gBAYX;;;;;;YAID,4BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,6CACA,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GA3IgB;KAAA", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/WordPuzzleGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface WordPuzzleGameProps {\n  target: string\n  words: string[]\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function WordPuzzleGame({ target, words, onComplete }: WordPuzzleGameProps) {\n  const [selectedWord, setSelectedWord] = useState<string | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleWordSelect = (word: string) => {\n    if (showResult) return\n    \n    setSelectedWord(word)\n    const correct_answer = word === target\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 1500)\n  }\n\n  const handleReset = () => {\n    setSelectedWord(null)\n    setShowResult(false)\n    setIsCorrect(false)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Словесная головоломка\n        </h2>\n        <p className=\"text-sm text-gray-600\">\n          Найдите слово, которое соответствует: <strong>\"{target}\"</strong>\n        </p>\n      </div>\n\n      {/* Selected Word Display */}\n      <Card className=\"bg-gray-50\">\n        <CardContent className=\"p-6 text-center\">\n          <div className=\"text-lg text-gray-600 mb-2\">Ваш выбор:</div>\n          <div className={`text-2xl font-bold p-4 rounded-lg border-2 border-dashed ${\n            selectedWord\n              ? showResult\n                ? isCorrect\n                  ? 'bg-green-100 border-green-300 text-green-800'\n                  : 'bg-red-100 border-red-300 text-red-800'\n                : 'bg-blue-100 border-blue-300 text-blue-800'\n              : 'bg-white border-gray-300 text-gray-400'\n          }`}>\n            {selectedWord || 'Выберите слово'}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Word Options */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {words.map((word, index) => {\n          let buttonClass = 'h-12 text-base'\n          let icon = null\n\n          if (showResult) {\n            if (word === target) {\n              buttonClass += ' bg-green-100 border-green-300 text-green-800'\n              icon = <CheckCircle className=\"h-4 w-4 text-green-600 ml-2\" />\n            } else if (word === selectedWord && word !== target) {\n              buttonClass += ' bg-red-100 border-red-300 text-red-800'\n              icon = <XCircle className=\"h-4 w-4 text-red-600 ml-2\" />\n            } else {\n              buttonClass += ' opacity-50'\n            }\n          } else if (word === selectedWord) {\n            buttonClass += ' bg-blue-100 border-blue-300 text-blue-800'\n          }\n\n          return (\n            <Button\n              key={index}\n              variant=\"outline\"\n              className={buttonClass}\n              onClick={() => handleWordSelect(word)}\n              disabled={showResult}\n            >\n              <div className=\"flex items-center justify-center\">\n                <span>{word}</span>\n                {icon}\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Reset Button */}\n      {selectedWord && !showResult && (\n        <div className=\"text-center\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleReset}\n            className=\"text-gray-600\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\n            Сбросить выбор\n          </Button>\n        </div>\n      )}\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Отлично! Вы нашли правильное слово.'\n                : `Правильный ответ: \"${target}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAcO,SAAS,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAuB;;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,gBAAgB;QAChB,MAAM,iBAAiB,SAAS;QAChC,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;;4BAAwB;0CACG,6LAAC;;oCAAO;oCAAE;oCAAO;;;;;;;;;;;;;;;;;;;0BAK3D,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,6LAAC;4BAAI,WAAW,CAAC,yDAAyD,EACxE,eACI,aACE,YACE,iDACA,2CACF,8CACF,0CACJ;sCACC,gBAAgB;;;;;;;;;;;;;;;;;0BAMvB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,IAAI,cAAc;oBAClB,IAAI,OAAO;oBAEX,IAAI,YAAY;wBACd,IAAI,SAAS,QAAQ;4BACnB,eAAe;4BACf,qBAAO,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;wBAChC,OAAO,IAAI,SAAS,gBAAgB,SAAS,QAAQ;4BACnD,eAAe;4BACf,qBAAO,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;wBAC5B,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO,IAAI,SAAS,cAAc;wBAChC,eAAe;oBACjB;oBAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW;wBACX,SAAS,IAAM,iBAAiB;wBAChC,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAM;;;;;;gCACN;;;;;;;uBARE;;;;;gBAYX;;;;;;YAID,gBAAgB,CAAC,4BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;YAO3C,4BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,wCACA,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQlD;GAvIgB;KAAA", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/SentenceBuilderGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface SentenceBuilderGameProps {\n  translation: string\n  correctOrder: string[]\n  extraWords: string[]\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function SentenceBuilderGame({ \n  translation, \n  correctOrder, \n  extraWords, \n  onComplete \n}: SentenceBuilderGameProps) {\n  const [selectedWords, setSelectedWords] = useState<string[]>([])\n  const [availableWords, setAvailableWords] = useState<string[]>([\n    ...correctOrder,\n    ...extraWords\n  ].sort(() => Math.random() - 0.5)) // Shuffle words\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleWordSelect = (word: string) => {\n    if (showResult) return\n    \n    setSelectedWords(prev => [...prev, word])\n    setAvailableWords(prev => prev.filter(w => w !== word))\n  }\n\n  const handleWordRemove = (index: number) => {\n    if (showResult) return\n    \n    const word = selectedWords[index]\n    setSelectedWords(prev => prev.filter((_, i) => i !== index))\n    setAvailableWords(prev => [...prev, word])\n  }\n\n  const handleCheck = () => {\n    const correct_answer = JSON.stringify(selectedWords) === JSON.stringify(correctOrder)\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 2000)\n  }\n\n  const handleReset = () => {\n    setSelectedWords([])\n    setAvailableWords([...correctOrder, ...extraWords].sort(() => Math.random() - 0.5))\n    setShowResult(false)\n    setIsCorrect(false)\n  }\n\n  const canCheck = selectedWords.length === correctOrder.length\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Составитель предложений\n        </h2>\n        <p className=\"text-sm text-gray-600 mb-2\">\n          Составьте английское предложение для:\n        </p>\n        <p className=\"text-lg font-medium text-blue-600\">\n          \"{translation}\"\n        </p>\n      </div>\n\n      {/* Sentence Building Area */}\n      <Card className=\"bg-gray-50\">\n        <CardContent className=\"p-4\">\n          <div className=\"text-sm text-gray-600 mb-2\">Ваше предложение:</div>\n          <div className={`min-h-[60px] p-3 rounded-lg border-2 border-dashed flex flex-wrap gap-2 items-center ${\n            showResult\n              ? isCorrect\n                ? 'bg-green-100 border-green-300'\n                : 'bg-red-100 border-red-300'\n              : 'bg-white border-gray-300'\n          }`}>\n            {selectedWords.length === 0 ? (\n              <span className=\"text-gray-400 italic\">Нажмите на слова ниже, чтобы составить предложение</span>\n            ) : (\n              selectedWords.map((word, index) => (\n                <Button\n                  key={index}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className={`${\n                    showResult\n                      ? isCorrect\n                        ? 'bg-green-200 border-green-400 text-green-800'\n                        : 'bg-red-200 border-red-400 text-red-800'\n                      : 'bg-blue-100 border-blue-300 text-blue-800 hover:bg-blue-200'\n                  }`}\n                  onClick={() => handleWordRemove(index)}\n                  disabled={showResult}\n                >\n                  {word}\n                </Button>\n              ))\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Available Words */}\n      <div>\n        <div className=\"text-sm text-gray-600 mb-3\">Доступные слова:</div>\n        <div className=\"flex flex-wrap gap-2\">\n          {availableWords.map((word, index) => (\n            <Button\n              key={index}\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handleWordSelect(word)}\n              disabled={showResult}\n              className=\"hover:bg-gray-100\"\n            >\n              {word}\n            </Button>\n          ))}\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex gap-3\">\n        {!showResult && (\n          <>\n            <Button\n              onClick={handleCheck}\n              disabled={!canCheck}\n              className=\"flex-1\"\n            >\n              Проверить ответ\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={handleReset}\n              disabled={selectedWords.length === 0}\n            >\n              <RotateCcw className=\"h-4 w-4\" />\n            </Button>\n          </>\n        )}\n      </div>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Отлично!' : 'Не совсем правильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Вы правильно составили предложение!'\n                : `Правильный порядок: \"${correctOrder.join(' ')}\"`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAeO,SAAS,oBAAoB,EAClC,WAAW,EACX,YAAY,EACZ,UAAU,EACV,UAAU,EACe;;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;WAC1D;WACA;KACJ,CAAC,IAAI;wCAAC,IAAM,KAAK,MAAM,KAAK;wCAAM,gBAAgB;;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAK;QACxC,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;IACnD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,MAAM,OAAO,aAAa,CAAC,MAAM;QACjC,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;aAAK;IAC3C;IAEA,MAAM,cAAc;QAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC;QACxE,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,iBAAiB,EAAE;QACnB,kBAAkB;eAAI;eAAiB;SAAW,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC9E,cAAc;QACd,aAAa;IACf;IAEA,MAAM,WAAW,cAAc,MAAM,KAAK,aAAa,MAAM;IAE7D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;;4BAAoC;4BAC7C;4BAAY;;;;;;;;;;;;;0BAKlB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,6LAAC;4BAAI,WAAW,CAAC,qFAAqF,EACpG,aACI,YACE,kCACA,8BACF,4BACJ;sCACC,cAAc,MAAM,KAAK,kBACxB,6LAAC;gCAAK,WAAU;0CAAuB;;;;;uCAEvC,cAAc,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,WAAW,GACT,aACI,YACE,iDACA,2CACF,+DACJ;oCACF,SAAS,IAAM,iBAAiB;oCAChC,UAAU;8CAET;mCAbI;;;;;;;;;;;;;;;;;;;;;0BAsBjB,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,UAAU;gCACV,WAAU;0CAET;+BAPI;;;;;;;;;;;;;;;;0BAcb,6LAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA;;sCACE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC;4BACX,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,cAAc,MAAM,KAAK;sCAEnC,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAO5B,4BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,aAAa;;;;;;sCAE5B,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,wCACA,CAAC,qBAAqB,EAAE,aAAa,IAAI,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GA1KgB;KAAA", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/ReadingGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, BookOpen } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface Question {\n  question: string\n  options: string[]\n  correct: string\n}\n\ninterface ReadingGameProps {\n  text: string\n  questions: Question[]\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function ReadingGame({ text, questions, onComplete }: ReadingGameProps) {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)\n  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([])\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n  const [showText, setShowText] = useState(true)\n\n  const currentQuestion = questions[currentQuestionIndex]\n  const isLastQuestion = currentQuestionIndex === questions.length - 1\n\n  const handleAnswerSelect = (answer: string) => {\n    if (showResult) return\n    \n    const correct_answer = answer === currentQuestion.correct\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Store the answer\n    const newAnswers = [...selectedAnswers]\n    newAnswers[currentQuestionIndex] = answer\n    setSelectedAnswers(newAnswers)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      if (isLastQuestion) {\n        // Calculate final score\n        const correctCount = newAnswers.filter((answer, index) => \n          answer === questions[index].correct\n        ).length\n        const score = Math.round((correctCount / questions.length) * 100)\n        onComplete(correctCount > 0, score)\n      } else {\n        // Move to next question\n        setCurrentQuestionIndex(prev => prev + 1)\n        setShowResult(false)\n        setIsCorrect(false)\n      }\n    }, 1500)\n  }\n\n  const toggleTextView = () => {\n    setShowText(!showText)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Понимание прочитанного\n        </h2>\n        <p className=\"text-sm text-gray-600\">\n          Вопрос {currentQuestionIndex + 1} из {questions.length}\n        </p>\n      </div>\n\n      {/* Text Toggle Button */}\n      <div className=\"text-center\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={toggleTextView}\n          className=\"mb-4\"\n        >\n          <BookOpen className=\"h-4 w-4 mr-2\" />\n          {showText ? 'Скрыть текст' : 'Показать текст'}\n        </Button>\n      </div>\n\n      {/* Reading Text */}\n      {showText && (\n        <Card className=\"bg-blue-50 border-blue-200\">\n          <CardHeader className=\"pb-3\">\n            <div className=\"flex items-center space-x-2\">\n              <BookOpen className=\"h-5 w-5 text-blue-600\" />\n              <h3 className=\"font-medium text-blue-900\">Текст для чтения</h3>\n            </div>\n          </CardHeader>\n          <CardContent className=\"pt-0\">\n            <p className=\"text-gray-800 leading-relaxed\">\n              {text}\n            </p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Question */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            {currentQuestion.question}\n          </h3>\n          \n          <div className=\"space-y-3\">\n            {currentQuestion.options.map((option, index) => {\n              let buttonClass = 'w-full text-left justify-start h-auto py-3 px-4'\n              let icon = null\n\n              if (showResult) {\n                if (option === currentQuestion.correct) {\n                  buttonClass += ' bg-green-100 border-green-300 text-green-800'\n                  icon = <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                } else if (option === selectedAnswers[currentQuestionIndex] && option !== currentQuestion.correct) {\n                  buttonClass += ' bg-red-100 border-red-300 text-red-800'\n                  icon = <XCircle className=\"h-5 w-5 text-red-600\" />\n                } else {\n                  buttonClass += ' opacity-50'\n                }\n              } else {\n                buttonClass += ' hover:bg-gray-50 border-gray-300'\n              }\n\n              return (\n                <Button\n                  key={index}\n                  variant=\"outline\"\n                  className={buttonClass}\n                  onClick={() => handleAnswerSelect(option)}\n                  disabled={showResult}\n                >\n                  <div className=\"flex items-center justify-between w-full\">\n                    <span className=\"text-base\">{option}</span>\n                    {icon}\n                  </div>\n                </Button>\n              )\n            })}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? isLastQuestion\n                  ? 'Отлично! Вы завершили упражнение по чтению!'\n                  : 'Хорошо! Переходим к следующему вопросу.'\n                : `Правильный ответ: \"${currentQuestion.correct}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Progress Indicator */}\n      <div className=\"flex justify-center space-x-2\">\n        {questions.map((_, index) => (\n          <div\n            key={index}\n            className={`w-2 h-2 rounded-full ${\n              index < currentQuestionIndex\n                ? 'bg-green-500'\n                : index === currentQuestionIndex\n                  ? 'bg-blue-500'\n                  : 'bg-gray-300'\n            }`}\n          />\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAoBO,SAAS,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAoB;;IAC3E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;IACvD,MAAM,iBAAiB,yBAAyB,UAAU,MAAM,GAAG;IAEnE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAEhB,MAAM,iBAAiB,WAAW,gBAAgB,OAAO;QACzD,aAAa;QACb,cAAc;QAEd,mBAAmB;QACnB,MAAM,aAAa;eAAI;SAAgB;QACvC,UAAU,CAAC,qBAAqB,GAAG;QACnC,mBAAmB;QAEnB,0BAA0B;QAC1B,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,IAAI,gBAAgB;gBAClB,wBAAwB;gBACxB,MAAM,eAAe,WAAW,MAAM,CAAC,CAAC,QAAQ,QAC9C,WAAW,SAAS,CAAC,MAAM,CAAC,OAAO,EACnC,MAAM;gBACR,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,eAAe,UAAU,MAAM,GAAI;gBAC7D,WAAW,eAAe,GAAG;YAC/B,OAAO;gBACL,wBAAwB;gBACxB,wBAAwB,CAAA,OAAQ,OAAO;gBACvC,cAAc;gBACd,aAAa;YACf;QACF,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,YAAY,CAAC;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC3B,uBAAuB;4BAAE;4BAAK,UAAU,MAAM;;;;;;;;;;;;;0BAK1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,WAAW,iBAAiB;;;;;;;;;;;;YAKhC,0BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAG,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAG9C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;0BAOT,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAG,WAAU;sCACX,gBAAgB,QAAQ;;;;;;sCAG3B,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;gCACpC,IAAI,cAAc;gCAClB,IAAI,OAAO;gCAEX,IAAI,YAAY;oCACd,IAAI,WAAW,gBAAgB,OAAO,EAAE;wCACtC,eAAe;wCACf,qBAAO,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;oCAChC,OAAO,IAAI,WAAW,eAAe,CAAC,qBAAqB,IAAI,WAAW,gBAAgB,OAAO,EAAE;wCACjG,eAAe;wCACf,qBAAO,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;oCAC5B,OAAO;wCACL,eAAe;oCACjB;gCACF,OAAO;oCACL,eAAe;gCACjB;gCAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAW;oCACX,SAAS,IAAM,mBAAmB;oCAClC,UAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAa;;;;;;4CAC5B;;;;;;;mCARE;;;;;4BAYX;;;;;;;;;;;;;;;;;YAML,4BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,iBACE,gDACA,4CACF,CAAC,mBAAmB,EAAE,gBAAgB,OAAO,CAAC,EAAE,CAAC;;;;;;;;;;;;;;;;;0BAQ7D,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,GAAG,sBACjB,6LAAC;wBAEC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,uBACJ,iBACA,UAAU,uBACR,gBACA,eACN;uBAPG;;;;;;;;;;;;;;;;AAajB;GAjLgB;KAAA", "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/MemoryMatchGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, Clock, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface CardData {\n  id: string\n  text: string\n  type: 'english' | 'russian'\n  pairId: string\n}\n\ninterface MemoryMatchGameProps {\n  wordPairs: Array<{ english: string; russian: string }>\n  timeLimit?: number // в секундах, по умолчанию 120 секунд (2 минуты)\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function MemoryMatchGame({ \n  wordPairs, \n  timeLimit = 120,\n  onComplete \n}: MemoryMatchGameProps) {\n  const [cards, setCards] = useState<CardData[]>([])\n  const [selectedCards, setSelectedCards] = useState<string[]>([])\n  const [matchedPairs, setMatchedPairs] = useState<string[]>([])\n  const [wrongPairs, setWrongPairs] = useState<string[]>([])\n  const [timeLeft, setTimeLeft] = useState(timeLimit)\n  const [gameStarted, setGameStarted] = useState(false)\n  const [gameEnded, setGameEnded] = useState(false)\n  const [score, setScore] = useState(0)\n\n  // Инициализация карточек\n  useEffect(() => {\n    const gameCards: CardData[] = []\n    \n    wordPairs.forEach((pair, index) => {\n      const pairId = `pair-${index}`\n      gameCards.push({\n        id: `en-${index}`,\n        text: pair.english,\n        type: 'english',\n        pairId\n      })\n      gameCards.push({\n        id: `ru-${index}`,\n        text: pair.russian,\n        type: 'russian',\n        pairId\n      })\n    })\n    \n    // Перемешиваем карточки\n    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)\n    setCards(shuffledCards)\n  }, [wordPairs])\n\n  // Таймер\n  useEffect(() => {\n    if (!gameStarted || gameEnded) return\n\n    const timer = setInterval(() => {\n      setTimeLeft((prev) => {\n        if (prev <= 1) {\n          setGameEnded(true)\n          onComplete(false, score)\n          return 0\n        }\n        return prev - 1\n      })\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [gameStarted, gameEnded, score, onComplete])\n\n  // Проверка завершения игры\n  useEffect(() => {\n    if (matchedPairs.length === wordPairs.length && wordPairs.length > 0) {\n      setGameEnded(true)\n      const finalScore = Math.max(0, 100 - Math.floor((timeLimit - timeLeft) / 2))\n      setScore(finalScore)\n      setTimeout(() => {\n        onComplete(true, finalScore)\n      }, 1500)\n    }\n  }, [matchedPairs.length, wordPairs.length, timeLimit, timeLeft, onComplete])\n\n  const startGame = () => {\n    setGameStarted(true)\n    triggerHapticFeedback('light')\n  }\n\n  const resetGame = () => {\n    setSelectedCards([])\n    setMatchedPairs([])\n    setWrongPairs([])\n    setTimeLeft(timeLimit)\n    setGameStarted(false)\n    setGameEnded(false)\n    setScore(0)\n    \n    // Перемешиваем карточки заново\n    const gameCards: CardData[] = []\n    wordPairs.forEach((pair, index) => {\n      const pairId = `pair-${index}`\n      gameCards.push({\n        id: `en-${index}`,\n        text: pair.english,\n        type: 'english',\n        pairId\n      })\n      gameCards.push({\n        id: `ru-${index}`,\n        text: pair.russian,\n        type: 'russian',\n        pairId\n      })\n    })\n    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)\n    setCards(shuffledCards)\n    \n    triggerHapticFeedback('light')\n  }\n\n  const handleCardClick = useCallback((cardId: string) => {\n    if (!gameStarted || gameEnded || selectedCards.includes(cardId) || matchedPairs.some(pairId => \n      cards.find(c => c.id === cardId)?.pairId === pairId\n    )) {\n      return\n    }\n\n    const newSelectedCards = [...selectedCards, cardId]\n    setSelectedCards(newSelectedCards)\n\n    if (newSelectedCards.length === 2) {\n      const [firstCardId, secondCardId] = newSelectedCards\n      const firstCard = cards.find(c => c.id === firstCardId)\n      const secondCard = cards.find(c => c.id === secondCardId)\n\n      if (firstCard && secondCard && firstCard.pairId === secondCard.pairId) {\n        // Правильная пара\n        setMatchedPairs(prev => [...prev, firstCard.pairId])\n        setSelectedCards([])\n        triggerHapticFeedback('light')\n      } else {\n        // Неправильная пара\n        setWrongPairs([firstCardId, secondCardId])\n        triggerHapticFeedback('heavy')\n        \n        setTimeout(() => {\n          setSelectedCards([])\n          setWrongPairs([])\n        }, 1000)\n      }\n    }\n  }, [gameStarted, gameEnded, selectedCards, matchedPairs, cards])\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getCardStyle = (card: CardData) => {\n    const isSelected = selectedCards.includes(card.id)\n    const isMatched = matchedPairs.includes(card.pairId)\n    const isWrong = wrongPairs.includes(card.id)\n    \n    let baseStyle = 'h-20 text-sm font-medium transition-all duration-200 '\n    \n    if (isMatched) {\n      baseStyle += 'bg-green-100 border-green-300 text-green-800 opacity-50'\n    } else if (isWrong) {\n      baseStyle += 'bg-red-100 border-red-300 text-red-800'\n    } else if (isSelected) {\n      baseStyle += 'bg-gray-100 border-gray-400 text-gray-800'\n    } else {\n      baseStyle += 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n    }\n    \n    return baseStyle\n  }\n\n  if (!gameStarted) {\n    return (\n      <div className=\"space-y-6 text-center\">\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Игра на сопоставление слов\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Найдите пары: английские слова и их русские переводы\n          </p>\n          <div className=\"flex items-center justify-center gap-2 text-sm text-gray-500 mb-6\">\n            <Clock className=\"h-4 w-4\" />\n            <span>Время: {formatTime(timeLimit)}</span>\n          </div>\n        </div>\n        \n        <Button onClick={startGame} className=\"px-8 py-3\">\n          Начать игру\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Заголовок и таймер */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">\n          Найдите пары слов\n        </h2>\n        <div className=\"flex items-center gap-4\">\n          <div className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${\n            timeLeft <= 30 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n          }`}>\n            <Clock className=\"h-4 w-4\" />\n            <span>{formatTime(timeLeft)}</span>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={resetGame}\n            className=\"p-2\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Прогресс */}\n      <div className=\"text-center text-sm text-gray-600\">\n        Найдено пар: {matchedPairs.length} из {wordPairs.length}\n      </div>\n\n      {/* Игровое поле */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {cards.map((card) => (\n          <Button\n            key={card.id}\n            variant=\"outline\"\n            className={getCardStyle(card)}\n            onClick={() => handleCardClick(card.id)}\n            disabled={gameEnded || matchedPairs.includes(card.pairId)}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-xs text-gray-500 mb-1\">\n                {card.type === 'english' ? 'EN' : 'RU'}\n              </div>\n              <div>{card.text}</div>\n            </div>\n          </Button>\n        ))}\n      </div>\n\n      {/* Результат игры */}\n      {gameEnded && (\n        <Card className={`${matchedPairs.length === wordPairs.length ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {matchedPairs.length === wordPairs.length ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${\n              matchedPairs.length === wordPairs.length ? 'text-green-800' : 'text-red-800'\n            }`}>\n              {matchedPairs.length === wordPairs.length ? 'Отлично!' : 'Время вышло!'}\n            </h3>\n            <p className={`text-sm ${\n              matchedPairs.length === wordPairs.length ? 'text-green-700' : 'text-red-700'\n            }`}>\n              {matchedPairs.length === wordPairs.length\n                ? `Вы нашли все пары! Счёт: ${score}`\n                : `Найдено пар: ${matchedPairs.length} из ${wordPairs.length}`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAqBO,SAAS,gBAAgB,EAC9B,SAAS,EACT,YAAY,GAAG,EACf,UAAU,EACW;;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,YAAwB,EAAE;YAEhC,UAAU,OAAO;6CAAC,CAAC,MAAM;oBACvB,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO;oBAC9B,UAAU,IAAI,CAAC;wBACb,IAAI,CAAC,GAAG,EAAE,OAAO;wBACjB,MAAM,KAAK,OAAO;wBAClB,MAAM;wBACN;oBACF;oBACA,UAAU,IAAI,CAAC;wBACb,IAAI,CAAC,GAAG,EAAE,OAAO;wBACjB,MAAM,KAAK,OAAO;wBAClB,MAAM;wBACN;oBACF;gBACF;;YAEA,wBAAwB;YACxB,MAAM,gBAAgB,UAAU,IAAI;2DAAC,IAAM,KAAK,MAAM,KAAK;;YAC3D,SAAS;QACX;oCAAG;QAAC;KAAU;IAEd,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,eAAe,WAAW;YAE/B,MAAM,QAAQ;mDAAY;oBACxB;2DAAY,CAAC;4BACX,IAAI,QAAQ,GAAG;gCACb,aAAa;gCACb,WAAW,OAAO;gCAClB,OAAO;4BACT;4BACA,OAAO,OAAO;wBAChB;;gBACF;kDAAG;YAEH;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;QAAa;QAAW;QAAO;KAAW;IAE9C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,aAAa,MAAM,KAAK,UAAU,MAAM,IAAI,UAAU,MAAM,GAAG,GAAG;gBACpE,aAAa;gBACb,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC,YAAY,QAAQ,IAAI;gBACzE,SAAS;gBACT;iDAAW;wBACT,WAAW,MAAM;oBACnB;gDAAG;YACL;QACF;oCAAG;QAAC,aAAa,MAAM;QAAE,UAAU,MAAM;QAAE;QAAW;QAAU;KAAW;IAE3E,MAAM,YAAY;QAChB,eAAe;QACf,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE;IACxB;IAEA,MAAM,YAAY;QAChB,iBAAiB,EAAE;QACnB,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,YAAY;QACZ,eAAe;QACf,aAAa;QACb,SAAS;QAET,+BAA+B;QAC/B,MAAM,YAAwB,EAAE;QAChC,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO;YAC9B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;YACA,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,GAAG,EAAE,OAAO;gBACjB,MAAM,KAAK,OAAO;gBAClB,MAAM;gBACN;YACF;QACF;QACA,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC3D,SAAS;QAET,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE;IACxB;IAEA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACnC,IAAI,CAAC,eAAe,aAAa,cAAc,QAAQ,CAAC,WAAW,aAAa,IAAI;gEAAC,CAAA,SACnF,MAAM,IAAI;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wEAAS,WAAW;gEAC5C;gBACD;YACF;YAEA,MAAM,mBAAmB;mBAAI;gBAAe;aAAO;YACnD,iBAAiB;YAEjB,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,MAAM,CAAC,aAAa,aAAa,GAAG;gBACpC,MAAM,YAAY,MAAM,IAAI;8EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAC3C,MAAM,aAAa,MAAM,IAAI;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAE5C,IAAI,aAAa,cAAc,UAAU,MAAM,KAAK,WAAW,MAAM,EAAE;oBACrE,kBAAkB;oBAClB;wEAAgB,CAAA,OAAQ;mCAAI;gCAAM,UAAU,MAAM;6BAAC;;oBACnD,iBAAiB,EAAE;oBACnB,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE;gBACxB,OAAO;oBACL,oBAAoB;oBACpB,cAAc;wBAAC;wBAAa;qBAAa;oBACzC,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE;oBAEtB;wEAAW;4BACT,iBAAiB,EAAE;4BACnB,cAAc,EAAE;wBAClB;uEAAG;gBACL;YACF;QACF;uDAAG;QAAC;QAAa;QAAW;QAAe;QAAc;KAAM;IAE/D,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QACjD,MAAM,YAAY,aAAa,QAAQ,CAAC,KAAK,MAAM;QACnD,MAAM,UAAU,WAAW,QAAQ,CAAC,KAAK,EAAE;QAE3C,IAAI,YAAY;QAEhB,IAAI,WAAW;YACb,aAAa;QACf,OAAO,IAAI,SAAS;YAClB,aAAa;QACf,OAAO,IAAI,YAAY;YACrB,aAAa;QACf,OAAO;YACL,aAAa;QACf;QAEA,OAAO;IACT;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;;wCAAK;wCAAQ,WAAW;;;;;;;;;;;;;;;;;;;8BAI7B,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAW,WAAU;8BAAY;;;;;;;;;;;;IAKxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,mEAAmE,EAClF,YAAY,KAAK,4BAA4B,6BAC7C;;kDACA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAM,WAAW;;;;;;;;;;;;0CAEpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM3B,6LAAC;gBAAI,WAAU;;oBAAoC;oBACnC,aAAa,MAAM;oBAAC;oBAAK,UAAU,MAAM;;;;;;;0BAIzD,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW,aAAa;wBACxB,SAAS,IAAM,gBAAgB,KAAK,EAAE;wBACtC,UAAU,aAAa,aAAa,QAAQ,CAAC,KAAK,MAAM;kCAExD,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,KAAK,YAAY,OAAO;;;;;;8CAEpC,6LAAC;8CAAK,KAAK,IAAI;;;;;;;;;;;;uBAVZ,KAAK,EAAE;;;;;;;;;;YAiBjB,2BACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,iCAAiC,4BAA4B;0BAC1H,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,KAAK,UAAU,MAAM,iBACvC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EACjC,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,mBAAmB,gBAC9D;sCACC,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,aAAa;;;;;;sCAE3D,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EACrB,aAAa,MAAM,KAAK,UAAU,MAAM,GAAG,mBAAmB,gBAC9D;sCACC,aAAa,MAAM,KAAK,UAAU,MAAM,GACrC,CAAC,yBAAyB,EAAE,OAAO,GACnC,CAAC,aAAa,EAAE,aAAa,MAAM,CAAC,IAAI,EAAE,UAAU,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAQ9E;GA3QgB;KAAA", "debugId": null}}, {"offset": {"line": 2099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/lesson/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { ArrowLeft, CheckCircle } from 'lucide-react'\nimport { getExercisesByLessonId, markExerciseComplete, updateUserXP } from '@/lib/database'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Database } from '@/lib/database.types'\nimport { Button } from '@/components/ui/Button'\nimport { ProgressBar } from '@/components/ui/ProgressBar'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { QuizGame } from '@/components/exercises/QuizGame'\nimport { FillInTheBlankGame } from '@/components/exercises/FillInTheBlankGame'\nimport { WordPuzzleGame } from '@/components/exercises/WordPuzzleGame'\nimport { SentenceBuilderGame } from '@/components/exercises/SentenceBuilderGame'\nimport { ReadingGame } from '@/components/exercises/ReadingGame'\nimport { Memory<PERSON>atchGame } from '@/components/exercises/MemoryMatchGame'\n\ntype Exercise = Database['public']['Tables']['exercises']['Row']\n\nexport default function LessonPage() {\n  const params = useParams()\n  const router = useRouter()\n  const { user } = useAuth()\n  const lessonId = params.id as string\n\n  const [exercises, setExercises] = useState<Exercise[]>([])\n  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0)\n  const [loading, setLoading] = useState(true)\n  const [completed, setCompleted] = useState(false)\n  const [totalXP, setTotalXP] = useState(0)\n\n  useEffect(() => {\n    if (lessonId) {\n      loadExercises()\n    }\n  }, [lessonId])\n\n  const loadExercises = async () => {\n    try {\n      setLoading(true)\n      const lessonExercises = await getExercisesByLessonId(lessonId)\n      setExercises(lessonExercises)\n    } catch (error) {\n      console.error('Error loading exercises:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExerciseComplete = async (correct: boolean, score?: number) => {\n    if (!user) return\n\n    const currentExercise = exercises[currentExerciseIndex]\n    \n    if (correct && currentExercise) {\n      // Mark exercise as complete\n      await markExerciseComplete(\n        user.id,\n        lessonId,\n        currentExercise.id,\n        score\n      )\n\n      // Add XP\n      const xpEarned = currentExercise.xp_reward\n      await updateUserXP(user.id, xpEarned)\n      setTotalXP(prev => prev + xpEarned)\n    }\n\n    // Move to next exercise or complete lesson\n    if (currentExerciseIndex < exercises.length - 1) {\n      setCurrentExerciseIndex(prev => prev + 1)\n    } else {\n      setCompleted(true)\n    }\n  }\n\n  const renderExercise = (exercise: Exercise) => {\n    const content = exercise.content_json as any\n\n    switch (exercise.type) {\n      case 'quiz':\n        return (\n          <QuizGame\n            question={content.question}\n            options={content.options}\n            correct={content.correct}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'fill-in-the-blank':\n        return (\n          <FillInTheBlankGame\n            sentence={content.sentence}\n            options={content.options}\n            correct={content.correct}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'word-puzzle':\n        return (\n          <WordPuzzleGame\n            target={content.target}\n            words={content.words}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'sentence-builder':\n        return (\n          <SentenceBuilderGame\n            translation={content.translation}\n            correctOrder={content.correct_order}\n            extraWords={content.extra_words}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'reading':\n        return (\n          <ReadingGame\n            text={content.text}\n            questions={content.questions}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'memory-match':\n        return (\n          <MemoryMatchGame\n            wordPairs={content.word_pairs}\n            timeLimit={content.time_limit}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      default:\n        return (\n          <div className=\"text-center py-8\">\n            <p className=\"text-gray-600\">\n              Exercise type \"{exercise.type}\" not implemented yet.\n            </p>\n            <Button \n              onClick={() => handleExerciseComplete(true)}\n              className=\"mt-4\"\n            >\n              Skip Exercise\n            </Button>\n          </div>\n        )\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (exercises.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Упражнения не найдены\n          </h1>\n          <p className=\"text-gray-600 mb-4\">\n            В этом уроке пока нет упражнений.\n          </p>\n          <Link href=\"/lessons\">\n            <Button>Назад к урокам</Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  if (completed) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center max-w-md mx-auto px-4\">\n          <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            Урок завершен!\n          </h1>\n          <p className=\"text-gray-600 mb-4\">\n            Отлично! Вы выполнили все упражнения в этом уроке.\n          </p>\n          <div className=\"bg-white rounded-lg p-4 mb-6\">\n            <div className=\"text-3xl font-bold text-green-600 mb-1\">\n              +{totalXP} XP\n            </div>\n            <div className=\"text-sm text-gray-600\">Опыта получено</div>\n          </div>\n          <div className=\"space-y-3\">\n            <Link href=\"/lessons\">\n              <Button className=\"w-full\">\n                Назад к урокам\n              </Button>\n            </Link>\n            <Link href=\"/\">\n              <Button variant=\"outline\" className=\"w-full\">\n                Главная\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  const currentExercise = exercises[currentExerciseIndex]\n  const progress = ((currentExerciseIndex + 1) / exercises.length) * 100\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-md mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <Link href=\"/lessons\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4\" />\n              </Button>\n            </Link>\n            <div className=\"flex-1\">\n              <h1 className=\"text-lg font-semibold text-gray-900\">\n                Упражнение {currentExerciseIndex + 1} из {exercises.length}\n              </h1>\n            </div>\n          </div>\n          <ProgressBar\n            value={currentExerciseIndex + 1}\n            max={exercises.length}\n            className=\"w-full\"\n          />\n        </div>\n      </div>\n\n      {/* Exercise Content */}\n      <div className=\"max-w-md mx-auto px-4 py-6\">\n        {renderExercise(currentExercise)}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;+BAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,kBAAkB,MAAM,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;YACrD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAAO,SAAkB;QACtD,IAAI,CAAC,MAAM;QAEX,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;QAEvD,IAAI,WAAW,iBAAiB;YAC9B,4BAA4B;YAC5B,MAAM,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD,EACvB,KAAK,EAAE,EACP,UACA,gBAAgB,EAAE,EAClB;YAGF,SAAS;YACT,MAAM,WAAW,gBAAgB,SAAS;YAC1C,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5B,WAAW,CAAA,OAAQ,OAAO;QAC5B;QAEA,2CAA2C;QAC3C,IAAI,uBAAuB,UAAU,MAAM,GAAG,GAAG;YAC/C,wBAAwB,CAAA,OAAQ,OAAO;QACzC,OAAO;YACL,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,SAAS,YAAY;QAErC,OAAQ,SAAS,IAAI;YACnB,KAAK;gBACH,qBACE,6LAAC,8IAAA,CAAA,WAAQ;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,SAAS,QAAQ,OAAO;oBACxB,SAAS,QAAQ,OAAO;oBACxB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,wJAAA,CAAA,qBAAkB;oBACjB,UAAU,QAAQ,QAAQ;oBAC1B,SAAS,QAAQ,OAAO;oBACxB,SAAS,QAAQ,OAAO;oBACxB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,oJAAA,CAAA,iBAAc;oBACb,QAAQ,QAAQ,MAAM;oBACtB,OAAO,QAAQ,KAAK;oBACpB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,yJAAA,CAAA,sBAAmB;oBAClB,aAAa,QAAQ,WAAW;oBAChC,cAAc,QAAQ,aAAa;oBACnC,YAAY,QAAQ,WAAW;oBAC/B,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,iJAAA,CAAA,cAAW;oBACV,MAAM,QAAQ,IAAI;oBAClB,WAAW,QAAQ,SAAS;oBAC5B,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,qJAAA,CAAA,kBAAe;oBACd,WAAW,QAAQ,UAAU;oBAC7B,WAAW,QAAQ,UAAU;oBAC7B,YAAY;;;;;;YAGlB;gBACE,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAgB;gCACX,SAAS,IAAI;gCAAC;;;;;;;sCAEhC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,uBAAuB;4BACtC,WAAU;sCACX;;;;;;;;;;;;QAKT;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAKlB;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAyC;oCACpD;oCAAQ;;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAS;;;;;;;;;;;0CAI7B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQzD;IAEA,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;IACvD,MAAM,WAAW,AAAC,CAAC,uBAAuB,CAAC,IAAI,UAAU,MAAM,GAAI;IAEnE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;;4CAAsC;4CACtC,uBAAuB;4CAAE;4CAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;sCAIhE,6LAAC,0IAAA,CAAA,cAAW;4BACV,OAAO,uBAAuB;4BAC9B,KAAK,UAAU,MAAM;4BACrB,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACZ,eAAe;;;;;;;;;;;;AAIxB;GAhOwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACP,kIAAA,CAAA,UAAO;;;KAHF", "debugId": null}}]}