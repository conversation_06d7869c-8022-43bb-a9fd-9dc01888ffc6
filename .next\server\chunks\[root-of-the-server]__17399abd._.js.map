{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/api/users/progress/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nconst supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// GET - получить прогресс пользователя\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const telegramId = searchParams.get('telegram_id')\n\n    if (!telegramId) {\n      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })\n    }\n\n    // Получаем пользователя\n    const { data: user, error: userError } = await supabase\n      .from('users')\n      .select('*')\n      .eq('telegram_id', parseInt(telegramId) || telegramId)\n      .single()\n\n    if (userError || !user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    // Получаем все уроки для уровня пользователя\n    const { data: lessons, error: lessonsError } = await supabase\n      .from('lessons')\n      .select('*')\n      .eq('level', user.level)\n      .order('order')\n\n    if (lessonsError) {\n      console.error('Error fetching lessons:', lessonsError)\n      return NextResponse.json({ error: lessonsError.message }, { status: 500 })\n    }\n\n    // Получаем прогресс пользователя\n    const { data: progress, error: progressError } = await supabase\n      .from('user_progress')\n      .select('*')\n      .eq('user_id', user.id)\n\n    if (progressError) {\n      console.error('Error fetching progress:', progressError)\n      // Если таблица прогресса не существует, возвращаем пустой прогресс\n      const progressData = {\n        user: {\n          id: user.id,\n          telegram_id: user.telegram_id,\n          nickname: user.username || 'User',\n          avatar: user.first_name || '👤',\n          level: user.level,\n          theme: 'light',\n          is_onboarded: true,\n          total_xp: user.xp || 0,\n          current_streak: 0,\n          last_activity_date: new Date().toISOString().split('T')[0],\n          created_at: user.created_at,\n          updated_at: user.updated_at\n        },\n        lessons: lessons || [],\n        progress: [],\n        stats: {\n          totalLessons: lessons?.length || 0,\n          completedLessons: 0,\n          totalXP: user.xp || 0,\n          currentStreak: 0,\n          nextLesson: lessons?.[0] || null\n        }\n      }\n      return NextResponse.json(progressData)\n    }\n\n    // Подсчитываем статистику\n    const completedLessons = progress?.filter(p => p.completed).length || 0\n    const nextLesson = lessons?.find(lesson => \n      !progress?.some(p => p.lesson_id === lesson.id && p.completed)\n    ) || null\n\n    const progressData = {\n      user: {\n        id: user.id,\n        telegram_id: user.telegram_id,\n        nickname: user.username || 'User',\n        avatar: user.first_name || '👤',\n        level: user.level,\n        theme: 'light',\n        is_onboarded: true,\n        total_xp: user.xp || 0,\n        current_streak: 0,\n        last_activity_date: new Date().toISOString().split('T')[0],\n        created_at: user.created_at,\n        updated_at: user.updated_at\n      },\n      lessons: lessons || [],\n      progress: progress || [],\n      stats: {\n        totalLessons: lessons?.length || 0,\n        completedLessons,\n        totalXP: user.xp || 0,\n        currentStreak: 0,\n        nextLesson\n      }\n    }\n\n    return NextResponse.json(progressData)\n  } catch (error) {\n    console.error('Error in GET /api/users/progress:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAGpC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,wBAAwB;QACxB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,SAAS,eAAe,YAC1C,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,6CAA6C;QAC7C,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,KAAK,KAAK,EACtB,KAAK,CAAC;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,aAAa,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,iCAAiC;QACjC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,mEAAmE;YACnE,MAAM,eAAe;gBACnB,MAAM;oBACJ,IAAI,KAAK,EAAE;oBACX,aAAa,KAAK,WAAW;oBAC7B,UAAU,KAAK,QAAQ,IAAI;oBAC3B,QAAQ,KAAK,UAAU,IAAI;oBAC3B,OAAO,KAAK,KAAK;oBACjB,OAAO;oBACP,cAAc;oBACd,UAAU,KAAK,EAAE,IAAI;oBACrB,gBAAgB;oBAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC1D,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;gBAC7B;gBACA,SAAS,WAAW,EAAE;gBACtB,UAAU,EAAE;gBACZ,OAAO;oBACL,cAAc,SAAS,UAAU;oBACjC,kBAAkB;oBAClB,SAAS,KAAK,EAAE,IAAI;oBACpB,eAAe;oBACf,YAAY,SAAS,CAAC,EAAE,IAAI;gBAC9B;YACF;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,0BAA0B;QAC1B,MAAM,mBAAmB,UAAU,OAAO,CAAA,IAAK,EAAE,SAAS,EAAE,UAAU;QACtE,MAAM,aAAa,SAAS,KAAK,CAAA,SAC/B,CAAC,UAAU,KAAK,CAAA,IAAK,EAAE,SAAS,KAAK,OAAO,EAAE,IAAI,EAAE,SAAS,MAC1D;QAEL,MAAM,eAAe;YACnB,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,QAAQ,KAAK,UAAU,IAAI;gBAC3B,OAAO,KAAK,KAAK;gBACjB,OAAO;gBACP,cAAc;gBACd,UAAU,KAAK,EAAE,IAAI;gBACrB,gBAAgB;gBAChB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1D,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;YAC7B;YACA,SAAS,WAAW,EAAE;YACtB,UAAU,YAAY,EAAE;YACxB,OAAO;gBACL,cAAc,SAAS,UAAU;gBACjC;gBACA,SAAS,KAAK,EAAE,IAAI;gBACpB,eAAe;gBACf;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}