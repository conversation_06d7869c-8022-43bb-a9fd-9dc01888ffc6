# 🔧 Руководство по работе с Админ панелью

## 📍 Доступ к админ панели

### Способы входа:
1. **Прямая ссылка**: http://localhost:3000/admin
2. **Через тестовую страницу**: http://localhost:3000/test → кнопка "Admin"
3. **Добавить `/admin` к основному URL**

## 📚 Создание уроков

### Шаг 1: Откройте вкладку "Lessons"
1. Перейдите в админ панель
2. Убедитесь, что выбрана вкладка "Lessons" (по умолчанию)

### Шаг 2: Заполните форму создания урока
- **Title** (Название): Введите название урока
  - Пример: "Basic Greetings", "Numbers 1-10"
- **Level** (Уровень): Выберите из выпадающего списка
  - A1, A2, B1, B2, C1, C2
- **Description** (Описание): Краткое описание урока
  - Пример: "Learn how to say hello and introduce yourself"
- **Order** (Порядок): Номер урока в рамках уровня
  - Начинайте с 1, затем 2, 3...

### Шаг 3: Сохранение
- Нажмите кнопку "Add Lesson"
- Урок будет добавлен в базу данных

## 📝 Создание упражнений

### Шаг 1: Откройте вкладку "Exercises"
1. Перейдите на вкладку "Exercises"
2. Найдите форму "Create New Exercise"

### Шаг 2: Выберите урок и тип
- **Lesson**: Выберите урок из выпадающего списка
- **Exercise Type**: Выберите тип упражнения:
  - Quiz (Викторина)
  - Fill in the Blank (Заполнить пропуски)
  - Word Puzzle (Головоломка со словами)
  - Sentence Builder (Конструктор предложений)
  - Reading (Чтение)
  - Memory Match (Сопоставление слов)
  - Audio Quiz (Аудио викторина)

### Шаг 3: Введите JSON контент
В поле "Exercise Content (JSON)" введите структурированные данные согласно выбранному типу упражнения.

## 🎮 JSON структуры для каждого типа упражнения

### 1. Quiz (Викторина)
```json
{
  "question": "How do you greet someone in the morning?",
  "options": ["Good morning", "Good night", "Good afternoon", "Good evening"],
  "correct": "Good morning"
}
```

**Поля:**
- `question`: Текст вопроса
- `options`: Массив из 4 вариантов ответа
- `correct`: Правильный ответ (должен совпадать с одним из options)

### 2. Fill in the Blank (Заполнить пропуски)
```json
{
  "sentence": "Hello, my name ___ John.",
  "options": ["is", "are", "am", "be"],
  "correct": "is"
}
```

**Поля:**
- `sentence`: Предложение с пропуском (используйте `___`)
- `options`: Варианты для заполнения пропуска
- `correct`: Правильный вариант

### 3. Memory Match (Сопоставление слов) ⭐ НОВОЕ
```json
{
  "word_pairs": [
    {"english": "Hello", "russian": "Привет"},
    {"english": "Good morning", "russian": "Доброе утро"},
    {"english": "Thank you", "russian": "Спасибо"},
    {"english": "Please", "russian": "Пожалуйста"},
    {"english": "Goodbye", "russian": "До свидания"},
    {"english": "Yes", "russian": "Да"}
  ]
}
```

**Поля:**
- `word_pairs`: Массив пар слов
- Каждая пара содержит `english` и `russian`
- **Рекомендуется**: 6 пар для оптимального игрового поля

### 4. Sentence Builder (Конструктор предложений)
```json
{
  "translation": "Меня зовут Анна.",
  "correct_order": ["My", "name", "is", "Anna"],
  "extra_words": ["am", "called", "the"]
}
```

**Поля:**
- `translation`: Русский перевод предложения
- `correct_order`: Правильный порядок английских слов
- `extra_words`: Дополнительные слова-отвлекатели

### 5. Word Puzzle (Головоломка со словами)
```json
{
  "target": "Hello",
  "words": ["Hello", "Hi", "Hey", "Goodbye"]
}
```

**Поля:**
- `target`: Целевое слово, которое нужно найти
- `words`: Массив слов, включая правильный ответ

### 6. Reading (Чтение с вопросами)
```json
{
  "text": "John is a student. He studies English every day. He likes reading books.",
  "questions": [
    {
      "question": "What does John study?",
      "options": ["Math", "English", "History", "Science"],
      "correct": "English"
    },
    {
      "question": "What does John like to do?",
      "options": ["Watch TV", "Play games", "Read books", "Listen to music"],
      "correct": "Read books"
    }
  ]
}
```

**Поля:**
- `text`: Текст для чтения
- `questions`: Массив вопросов по тексту
- Каждый вопрос имеет структуру как в Quiz

## 💡 Советы и рекомендации

### Создание качественного контента:

1. **Прогрессия сложности**: 
   - A1: Простые слова и фразы
   - A2: Базовая грамматика
   - B1-B2: Сложные конструкции
   - C1-C2: Продвинутая лексика

2. **Memory Match игры**:
   - Используйте тематические группы слов
   - Избегайте слишком похожих переводов
   - 6 пар = оптимальное количество для мобильного экрана

3. **Quiz вопросы**:
   - Делайте вопросы четкими и однозначными
   - Варианты ответов должны быть правдоподобными
   - Избегайте слишком очевидных неправильных ответов

4. **Fill in the Blank**:
   - Пропуск должен быть логичным
   - Предоставляйте разумные альтернативы
   - Контекст должен помогать найти ответ

### Порядок создания контента:

1. **Сначала создайте уроки** для всех уровней
2. **Затем добавляйте упражнения** к каждому уроку
3. **Рекомендуемый порядок упражнений в уроке**:
   - Quiz (введение темы)
   - Fill in the Blank (практика)
   - Memory Match (закрепление лексики)
   - Sentence Builder (применение)
   - Reading (комплексная проверка)

## 🚨 Частые ошибки

1. **Неправильный JSON формат**:
   - Проверяйте запятые и кавычки
   - Используйте двойные кавычки `"`, не одинарные `'`

2. **Несоответствие типов**:
   - Убедитесь, что JSON структура соответствует выбранному типу упражнения

3. **Пустые поля**:
   - Все обязательные поля должны быть заполнены

4. **Дублирование порядка**:
   - В рамках одного урока каждое упражнение должно иметь уникальный номер order

## 📦 Массовая загрузка

Для загрузки большого количества контента используйте раздел "Bulk Upload":

1. Подготовьте JSON файл с массивом уроков
2. Перетащите файл в область загрузки
3. Нажмите "Choose File" для выбора файла

**Формат файла для массовой загрузки:**
```json
[
  {
    "title": "Basic Greetings",
    "level": "A1",
    "description": "Learn basic greetings",
    "order": 1
  },
  {
    "title": "Numbers 1-10", 
    "level": "A1",
    "description": "Learn numbers from 1 to 10",
    "order": 2
  }
]
```

---

Следуя этому руководству, вы сможете эффективно создавать образовательный контент для вашего Telegram Mini App! 🎓
