{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/onboarding/level/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { GraduationCap, Star, Award, Crown, Zap, Sparkles } from 'lucide-react'\nimport { useRouter } from 'next/navigation'\nimport { useUser } from '@/contexts/UserContext'\n\nconst LEVELS = [\n  {\n    code: 'A1',\n    name: 'Начинающий',\n    description: 'Изучаю первые слова и фразы',\n    icon: Star,\n    color: 'from-green-500 to-emerald-500',\n    bgColor: 'bg-green-50 dark:bg-green-900/20',\n    borderColor: 'border-green-200 dark:border-green-700'\n  },\n  {\n    code: 'A2',\n    name: 'Элементарный',\n    description: 'Понимаю простые предложения',\n    icon: GraduationCap,\n    color: 'from-blue-500 to-cyan-500',\n    bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n    borderColor: 'border-blue-200 dark:border-blue-700'\n  },\n  {\n    code: 'B1',\n    name: 'Средний',\n    description: 'Могу поддержать разговор',\n    icon: Award,\n    color: 'from-orange-500 to-amber-500',\n    bgColor: 'bg-orange-50 dark:bg-orange-900/20',\n    borderColor: 'border-orange-200 dark:border-orange-700'\n  },\n  {\n    code: 'B2',\n    name: 'Выше среднего',\n    description: 'Свободно выражаю мысли',\n    icon: Zap,\n    color: 'from-purple-500 to-violet-500',\n    bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n    borderColor: 'border-purple-200 dark:border-purple-700'\n  },\n  {\n    code: 'C1',\n    name: 'Продвинутый',\n    description: 'Понимаю сложные тексты',\n    icon: Crown,\n    color: 'from-red-500 to-pink-500',\n    bgColor: 'bg-red-50 dark:bg-red-900/20',\n    borderColor: 'border-red-200 dark:border-red-700'\n  },\n  {\n    code: 'C2',\n    name: 'Профессиональный',\n    description: 'Владею языком в совершенстве',\n    icon: Sparkles,\n    color: 'from-indigo-500 to-purple-500',\n    bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',\n    borderColor: 'border-indigo-200 dark:border-indigo-700'\n  }\n]\n\nexport default function LevelPage() {\n  const router = useRouter()\n  const { setUser } = useUser()\n  const [selectedLevel, setSelectedLevel] = useState('')\n  const [isCreating, setIsCreating] = useState(false)\n\n  const handleComplete = async () => {\n    if (!selectedLevel) return\n\n    setIsCreating(true)\n    try {\n      // Получаем данные из localStorage\n      const nickname = localStorage.getItem('onboarding_nickname')\n      const avatar = localStorage.getItem('onboarding_avatar')\n      const telegramId = localStorage.getItem('telegram_user_id') || Date.now().toString()\n\n      // Создаем пользователя\n      const response = await fetch('/api/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          telegramId,\n          nickname,\n          avatar,\n          level: selectedLevel,\n          theme: 'light'\n        }),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        // Сохраняем telegram_id для будущих сессий\n        localStorage.setItem('telegram_user_id', telegramId)\n\n        // Очищаем временные данные\n        localStorage.removeItem('onboarding_nickname')\n        localStorage.removeItem('onboarding_avatar')\n\n        // Устанавливаем пользователя в контекст\n        setUser(data.user)\n\n        console.log('User created/updated successfully:', data.user)\n\n        // Переходим на главную страницу\n        router.push('/')\n      } else {\n        console.error('Error creating user:', data.error)\n\n        // Если пользователь уже существует, это не ошибка - просто перенаправляем\n        if (response.status === 409) {\n          console.log('User already exists, redirecting to home')\n          router.push('/')\n        } else {\n          alert('Ошибка создания профиля: ' + data.error)\n        }\n      }\n    } catch (error) {\n      console.error('Error creating user:', error)\n      alert('Ошибка создания профиля')\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 p-4\">\n      <div className=\"max-w-2xl mx-auto\">\n        <Card className=\"shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm mb-6\">\n          <CardHeader className=\"text-center\">\n            <div className=\"mb-4\">\n              <GraduationCap className=\"h-12 w-12 text-indigo-600 dark:text-indigo-400 mx-auto\" />\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Выбери свой уровень\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Это поможет подобрать подходящие уроки\n            </p>\n          </CardHeader>\n        </Card>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n          {LEVELS.map((level) => {\n            const IconComponent = level.icon\n            const isSelected = selectedLevel === level.code\n            \n            return (\n              <Card\n                key={level.code}\n                className={`cursor-pointer transition-all duration-200 hover:scale-105 ${\n                  isSelected\n                    ? `${level.bgColor} ${level.borderColor} border-2 shadow-lg`\n                    : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md'\n                }`}\n                onClick={() => setSelectedLevel(level.code)}\n              >\n                <CardContent className=\"p-6 text-center\">\n                  <div className={`inline-flex p-3 rounded-full bg-gradient-to-r ${level.color} mb-4`}>\n                    <IconComponent className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-lg font-bold text-gray-900 dark:text-white mb-1\">\n                    {level.code} - {level.name}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    {level.description}\n                  </p>\n                </CardContent>\n              </Card>\n            )\n          })}\n        </div>\n\n        <Card className=\"shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\">\n          <CardContent className=\"p-6\">\n            <Button\n              onClick={handleComplete}\n              disabled={!selectedLevel || isCreating}\n              className=\"w-full py-3 text-lg font-medium bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isCreating ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                  Создаем профиль...\n                </>\n              ) : (\n                'Начать изучение! 🚀'\n              )}\n            </Button>\n            \n            {selectedLevel && (\n              <p className=\"text-center text-sm text-gray-600 dark:text-gray-300 mt-3\">\n                Выбран уровень: <span className=\"font-medium\">{selectedLevel}</span>\n              </p>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,SAAS;IACb;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,SAAS;QACT,aAAa;IACf;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB;QACrB,IAAI,CAAC,eAAe;QAEpB,cAAc;QACd,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,MAAM,aAAa,aAAa,OAAO,CAAC,uBAAuB,KAAK,GAAG,GAAG,QAAQ;YAElF,uBAAuB;YACvB,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA,OAAO;oBACP,OAAO;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,2CAA2C;gBAC3C,aAAa,OAAO,CAAC,oBAAoB;gBAEzC,2BAA2B;gBAC3B,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,wCAAwC;gBACxC,QAAQ,KAAK,IAAI;gBAEjB,QAAQ,GAAG,CAAC,sCAAsC,KAAK,IAAI;gBAE3D,gCAAgC;gBAChC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,QAAQ,KAAK,CAAC,wBAAwB,KAAK,KAAK;gBAEhD,0EAA0E;gBAC1E,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,MAAM,8BAA8B,KAAK,KAAK;gBAChD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;8BAMpD,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC;wBACX,MAAM,gBAAgB,MAAM,IAAI;wBAChC,MAAM,aAAa,kBAAkB,MAAM,IAAI;wBAE/C,qBACE,8OAAC,gIAAA,CAAA,OAAI;4BAEH,WAAW,CAAC,2DAA2D,EACrE,aACI,GAAG,MAAM,OAAO,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,mBAAmB,CAAC,GAC1D,yFACJ;4BACF,SAAS,IAAM,iBAAiB,MAAM,IAAI;sCAE1C,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAW,CAAC,8CAA8C,EAAE,MAAM,KAAK,CAAC,KAAK,CAAC;kDACjF,cAAA,8OAAC;4CAAc,WAAU;;;;;;;;;;;kDAE3B,8OAAC;wCAAG,WAAU;;4CACX,MAAM,IAAI;4CAAC;4CAAI,MAAM,IAAI;;;;;;;kDAE5B,8OAAC;wCAAE,WAAU;kDACV,MAAM,WAAW;;;;;;;;;;;;2BAhBjB,MAAM,IAAI;;;;;oBAqBrB;;;;;;8BAGF,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,iBAAiB;gCAC5B,WAAU;0CAET,2BACC;;sDACE,8OAAC;4CAAI,WAAU;;;;;;wCAAuE;;mDAIxF;;;;;;4BAIH,+BACC,8OAAC;gCAAE,WAAU;;oCAA4D;kDACvD,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D", "debugId": null}}]}