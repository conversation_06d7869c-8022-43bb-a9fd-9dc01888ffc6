{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/ProgressBar.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ProgressBarProps {\n  value: number\n  max: number\n  className?: string\n  showLabel?: boolean\n  label?: string\n}\n\nexport function ProgressBar({ \n  value, \n  max, \n  className, \n  showLabel = false, \n  label \n}: ProgressBarProps) {\n  const percentage = Math.min((value / max) * 100, 100)\n  \n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>{label}</span>\n          <span>{value}/{max}</span>\n        </div>\n      )}\n      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n        <div\n          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,SAAS,YAAY,EAC1B,KAAK,EACL,GAAG,EACH,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACY;IACjB,MAAM,aAAa,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK;IAEjD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM;;;;;;kCACP,8OAAC;;4BAAM;4BAAM;4BAAE;;;;;;;;;;;;;0BAGnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/QuizGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface QuizGameProps {\n  question: string\n  options: string[]\n  correct: string\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function QuizGame({ question, options, correct, onComplete }: QuizGameProps) {\n  const [selectedOption, setSelectedOption] = useState<string | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleOptionSelect = (option: string) => {\n    if (showResult) return\n    \n    setSelectedOption(option)\n    const correct_answer = option === correct\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 1500)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Question */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Вопрос викторины\n        </h2>\n        <p className=\"text-lg text-gray-700\">\n          {question}\n        </p>\n      </div>\n\n      {/* Options */}\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          let buttonClass = 'w-full text-left justify-start h-auto py-4 px-4'\n          let icon = null\n\n          if (showResult) {\n            if (option === correct) {\n              buttonClass += ' bg-green-100 border-green-300 text-green-800'\n              icon = <CheckCircle className=\"h-5 w-5 text-green-600\" />\n            } else if (option === selectedOption && option !== correct) {\n              buttonClass += ' bg-red-100 border-red-300 text-red-800'\n              icon = <XCircle className=\"h-5 w-5 text-red-600\" />\n            } else {\n              buttonClass += ' opacity-50'\n            }\n          } else {\n            buttonClass += ' hover:bg-gray-50 border-gray-300'\n          }\n\n          return (\n            <Button\n              key={index}\n              variant=\"outline\"\n              className={buttonClass}\n              onClick={() => handleOptionSelect(option)}\n              disabled={showResult}\n            >\n              <div className=\"flex items-center justify-between w-full\">\n                <span className=\"text-base\">{option}</span>\n                {icon}\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Отлично! Вы ответили правильно.'\n                : `Правильный ответ: \"${correct}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAeO,SAAS,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAiB;IAChF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAEhB,kBAAkB;QAClB,MAAM,iBAAiB,WAAW;QAClC,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;oBACpB,IAAI,cAAc;oBAClB,IAAI,OAAO;oBAEX,IAAI,YAAY;wBACd,IAAI,WAAW,SAAS;4BACtB,eAAe;4BACf,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;wBAChC,OAAO,IAAI,WAAW,kBAAkB,WAAW,SAAS;4BAC1D,eAAe;4BACf,qBAAO,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;wBAC5B,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO;wBACL,eAAe;oBACjB;oBAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW;wBACX,SAAS,IAAM,mBAAmB;wBAClC,UAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAa;;;;;;gCAC5B;;;;;;;uBARE;;;;;gBAYX;;;;;;YAID,4BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,oCACA,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/FillInTheBlankGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface FillInTheBlankGameProps {\n  sentence: string\n  options: string[]\n  correct: string\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function FillInTheBlankGame({ \n  sentence, \n  options, \n  correct, \n  onComplete \n}: FillInTheBlankGameProps) {\n  const [selectedOption, setSelectedOption] = useState<string | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleOptionSelect = (option: string) => {\n    if (showResult) return\n    \n    setSelectedOption(option)\n    const correct_answer = option === correct\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 1500)\n  }\n\n  // Split sentence by blank (represented by ___)\n  const renderSentence = () => {\n    const parts = sentence.split('___')\n    if (parts.length !== 2) {\n      // Fallback if sentence doesn't have exactly one blank\n      return (\n        <span className=\"text-lg\">\n          {sentence.replace('___', `[${selectedOption || '___'}]`)}\n        </span>\n      )\n    }\n\n    return (\n      <span className=\"text-lg\">\n        {parts[0]}\n        <span className={`inline-block min-w-[80px] px-2 py-1 mx-1 rounded border-2 border-dashed text-center font-medium ${\n          showResult\n            ? isCorrect\n              ? 'bg-green-100 border-green-300 text-green-800'\n              : 'bg-red-100 border-red-300 text-red-800'\n            : selectedOption\n              ? 'bg-blue-100 border-blue-300 text-blue-800'\n              : 'bg-gray-100 border-gray-300 text-gray-500'\n        }`}>\n          {selectedOption || '___'}\n        </span>\n        {parts[1]}\n      </span>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Заполните пропуск\n        </h2>\n        <p className=\"text-sm text-gray-600\">\n          Выберите правильное слово для завершения предложения\n        </p>\n      </div>\n\n      {/* Sentence with blank */}\n      <Card className=\"bg-gray-50\">\n        <CardContent className=\"p-6 text-center\">\n          {renderSentence()}\n        </CardContent>\n      </Card>\n\n      {/* Options */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {options.map((option, index) => {\n          let buttonClass = 'h-12'\n          let icon = null\n\n          if (showResult) {\n            if (option === correct) {\n              buttonClass += ' bg-green-100 border-green-300 text-green-800'\n              icon = <CheckCircle className=\"h-4 w-4 text-green-600 ml-2\" />\n            } else if (option === selectedOption && option !== correct) {\n              buttonClass += ' bg-red-100 border-red-300 text-red-800'\n              icon = <XCircle className=\"h-4 w-4 text-red-600 ml-2\" />\n            } else {\n              buttonClass += ' opacity-50'\n            }\n          } else if (option === selectedOption) {\n            buttonClass += ' bg-blue-100 border-blue-300 text-blue-800'\n          }\n\n          return (\n            <Button\n              key={index}\n              variant=\"outline\"\n              className={buttonClass}\n              onClick={() => handleOptionSelect(option)}\n              disabled={showResult}\n            >\n              <div className=\"flex items-center justify-center\">\n                <span>{option}</span>\n                {icon}\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Отлично! Вы правильно заполнили пропуск.'\n                : `Правильный ответ: \"${correct}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAeO,SAAS,mBAAmB,EACjC,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACc;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAEhB,kBAAkB;QAClB,MAAM,iBAAiB,WAAW;QAClC,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,+CAA+C;IAC/C,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,sDAAsD;YACtD,qBACE,8OAAC;gBAAK,WAAU;0BACb,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,kBAAkB,MAAM,CAAC,CAAC;;;;;;QAG7D;QAEA,qBACE,8OAAC;YAAK,WAAU;;gBACb,KAAK,CAAC,EAAE;8BACT,8OAAC;oBAAK,WAAW,CAAC,gGAAgG,EAChH,aACI,YACE,iDACA,2CACF,iBACE,8CACA,6CACN;8BACC,kBAAkB;;;;;;gBAEpB,KAAK,CAAC,EAAE;;;;;;;IAGf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;oBACpB,IAAI,cAAc;oBAClB,IAAI,OAAO;oBAEX,IAAI,YAAY;wBACd,IAAI,WAAW,SAAS;4BACtB,eAAe;4BACf,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;wBAChC,OAAO,IAAI,WAAW,kBAAkB,WAAW,SAAS;4BAC1D,eAAe;4BACf,qBAAO,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;wBAC5B,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO,IAAI,WAAW,gBAAgB;wBACpC,eAAe;oBACjB;oBAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW;wBACX,SAAS,IAAM,mBAAmB;wBAClC,UAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAM;;;;;;gCACN;;;;;;;uBARE;;;;;gBAYX;;;;;;YAID,4BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,6CACA,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/WordPuzzleGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface WordPuzzleGameProps {\n  target: string\n  words: string[]\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function WordPuzzleGame({ target, words, onComplete }: WordPuzzleGameProps) {\n  const [selectedWord, setSelectedWord] = useState<string | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleWordSelect = (word: string) => {\n    if (showResult) return\n    \n    setSelectedWord(word)\n    const correct_answer = word === target\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 1500)\n  }\n\n  const handleReset = () => {\n    setSelectedWord(null)\n    setShowResult(false)\n    setIsCorrect(false)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Словесная головоломка\n        </h2>\n        <p className=\"text-sm text-gray-600\">\n          Найдите слово, которое соответствует: <strong>\"{target}\"</strong>\n        </p>\n      </div>\n\n      {/* Selected Word Display */}\n      <Card className=\"bg-gray-50\">\n        <CardContent className=\"p-6 text-center\">\n          <div className=\"text-lg text-gray-600 mb-2\">Ваш выбор:</div>\n          <div className={`text-2xl font-bold p-4 rounded-lg border-2 border-dashed ${\n            selectedWord\n              ? showResult\n                ? isCorrect\n                  ? 'bg-green-100 border-green-300 text-green-800'\n                  : 'bg-red-100 border-red-300 text-red-800'\n                : 'bg-blue-100 border-blue-300 text-blue-800'\n              : 'bg-white border-gray-300 text-gray-400'\n          }`}>\n            {selectedWord || 'Выберите слово'}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Word Options */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {words.map((word, index) => {\n          let buttonClass = 'h-12 text-base'\n          let icon = null\n\n          if (showResult) {\n            if (word === target) {\n              buttonClass += ' bg-green-100 border-green-300 text-green-800'\n              icon = <CheckCircle className=\"h-4 w-4 text-green-600 ml-2\" />\n            } else if (word === selectedWord && word !== target) {\n              buttonClass += ' bg-red-100 border-red-300 text-red-800'\n              icon = <XCircle className=\"h-4 w-4 text-red-600 ml-2\" />\n            } else {\n              buttonClass += ' opacity-50'\n            }\n          } else if (word === selectedWord) {\n            buttonClass += ' bg-blue-100 border-blue-300 text-blue-800'\n          }\n\n          return (\n            <Button\n              key={index}\n              variant=\"outline\"\n              className={buttonClass}\n              onClick={() => handleWordSelect(word)}\n              disabled={showResult}\n            >\n              <div className=\"flex items-center justify-center\">\n                <span>{word}</span>\n                {icon}\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Reset Button */}\n      {selectedWord && !showResult && (\n        <div className=\"text-center\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleReset}\n            className=\"text-gray-600\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\n            Сбросить выбор\n          </Button>\n        </div>\n      )}\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Отлично! Вы нашли правильное слово.'\n                : `Правильный ответ: \"${target}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAcO,SAAS,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAuB;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,gBAAgB;QAChB,MAAM,iBAAiB,SAAS;QAChC,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;;4BAAwB;0CACG,8OAAC;;oCAAO;oCAAE;oCAAO;;;;;;;;;;;;;;;;;;;0BAK3D,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,8OAAC;4BAAI,WAAW,CAAC,yDAAyD,EACxE,eACI,aACE,YACE,iDACA,2CACF,8CACF,0CACJ;sCACC,gBAAgB;;;;;;;;;;;;;;;;;0BAMvB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,IAAI,cAAc;oBAClB,IAAI,OAAO;oBAEX,IAAI,YAAY;wBACd,IAAI,SAAS,QAAQ;4BACnB,eAAe;4BACf,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;wBAChC,OAAO,IAAI,SAAS,gBAAgB,SAAS,QAAQ;4BACnD,eAAe;4BACf,qBAAO,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;wBAC5B,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO,IAAI,SAAS,cAAc;wBAChC,eAAe;oBACjB;oBAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,WAAW;wBACX,SAAS,IAAM,iBAAiB;wBAChC,UAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAM;;;;;;gCACN;;;;;;;uBARE;;;;;gBAYX;;;;;;YAID,gBAAgB,CAAC,4BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;YAO3C,4BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,wCACA,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/SentenceBuilderGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, RotateCcw } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface SentenceBuilderGameProps {\n  translation: string\n  correctOrder: string[]\n  extraWords: string[]\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function SentenceBuilderGame({ \n  translation, \n  correctOrder, \n  extraWords, \n  onComplete \n}: SentenceBuilderGameProps) {\n  const [selectedWords, setSelectedWords] = useState<string[]>([])\n  const [availableWords, setAvailableWords] = useState<string[]>([\n    ...correctOrder,\n    ...extraWords\n  ].sort(() => Math.random() - 0.5)) // Shuffle words\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n\n  const handleWordSelect = (word: string) => {\n    if (showResult) return\n    \n    setSelectedWords(prev => [...prev, word])\n    setAvailableWords(prev => prev.filter(w => w !== word))\n  }\n\n  const handleWordRemove = (index: number) => {\n    if (showResult) return\n    \n    const word = selectedWords[index]\n    setSelectedWords(prev => prev.filter((_, i) => i !== index))\n    setAvailableWords(prev => [...prev, word])\n  }\n\n  const handleCheck = () => {\n    const correct_answer = JSON.stringify(selectedWords) === JSON.stringify(correctOrder)\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      onComplete(correct_answer, correct_answer ? 100 : 0)\n    }, 2000)\n  }\n\n  const handleReset = () => {\n    setSelectedWords([])\n    setAvailableWords([...correctOrder, ...extraWords].sort(() => Math.random() - 0.5))\n    setShowResult(false)\n    setIsCorrect(false)\n  }\n\n  const canCheck = selectedWords.length === correctOrder.length\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Составитель предложений\n        </h2>\n        <p className=\"text-sm text-gray-600 mb-2\">\n          Составьте английское предложение для:\n        </p>\n        <p className=\"text-lg font-medium text-blue-600\">\n          \"{translation}\"\n        </p>\n      </div>\n\n      {/* Sentence Building Area */}\n      <Card className=\"bg-gray-50\">\n        <CardContent className=\"p-4\">\n          <div className=\"text-sm text-gray-600 mb-2\">Ваше предложение:</div>\n          <div className={`min-h-[60px] p-3 rounded-lg border-2 border-dashed flex flex-wrap gap-2 items-center ${\n            showResult\n              ? isCorrect\n                ? 'bg-green-100 border-green-300'\n                : 'bg-red-100 border-red-300'\n              : 'bg-white border-gray-300'\n          }`}>\n            {selectedWords.length === 0 ? (\n              <span className=\"text-gray-400 italic\">Нажмите на слова ниже, чтобы составить предложение</span>\n            ) : (\n              selectedWords.map((word, index) => (\n                <Button\n                  key={index}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className={`${\n                    showResult\n                      ? isCorrect\n                        ? 'bg-green-200 border-green-400 text-green-800'\n                        : 'bg-red-200 border-red-400 text-red-800'\n                      : 'bg-blue-100 border-blue-300 text-blue-800 hover:bg-blue-200'\n                  }`}\n                  onClick={() => handleWordRemove(index)}\n                  disabled={showResult}\n                >\n                  {word}\n                </Button>\n              ))\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Available Words */}\n      <div>\n        <div className=\"text-sm text-gray-600 mb-3\">Доступные слова:</div>\n        <div className=\"flex flex-wrap gap-2\">\n          {availableWords.map((word, index) => (\n            <Button\n              key={index}\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handleWordSelect(word)}\n              disabled={showResult}\n              className=\"hover:bg-gray-100\"\n            >\n              {word}\n            </Button>\n          ))}\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex gap-3\">\n        {!showResult && (\n          <>\n            <Button\n              onClick={handleCheck}\n              disabled={!canCheck}\n              className=\"flex-1\"\n            >\n              Проверить ответ\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={handleReset}\n              disabled={selectedWords.length === 0}\n            >\n              <RotateCcw className=\"h-4 w-4\" />\n            </Button>\n          </>\n        )}\n      </div>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Отлично!' : 'Не совсем правильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? 'Вы правильно составили предложение!'\n                : `Правильный порядок: \"${correctOrder.join(' ')}\"`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAeO,SAAS,oBAAoB,EAClC,WAAW,EACX,YAAY,EACZ,UAAU,EACV,UAAU,EACe;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;WAC1D;WACA;KACJ,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK,MAAM,gBAAgB;;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAK;QACxC,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;IACnD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY;QAEhB,MAAM,OAAO,aAAa,CAAC,MAAM;QACjC,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;aAAK;IAC3C;IAEA,MAAM,cAAc;QAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC;QACxE,aAAa;QACb,cAAc;QAEd,0BAA0B;QAC1B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,WAAW,gBAAgB,iBAAiB,MAAM;QACpD,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,iBAAiB,EAAE;QACnB,kBAAkB;eAAI;eAAiB;SAAW,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QAC9E,cAAc;QACd,aAAa;IACf;IAEA,MAAM,WAAW,cAAc,MAAM,KAAK,aAAa,MAAM;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAE,WAAU;;4BAAoC;4BAC7C;4BAAY;;;;;;;;;;;;;0BAKlB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,8OAAC;4BAAI,WAAW,CAAC,qFAAqF,EACpG,aACI,YACE,kCACA,8BACF,4BACJ;sCACC,cAAc,MAAM,KAAK,kBACxB,8OAAC;gCAAK,WAAU;0CAAuB;;;;;uCAEvC,cAAc,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,WAAW,GACT,aACI,YACE,iDACA,2CACF,+DACJ;oCACF,SAAS,IAAM,iBAAiB;oCAChC,UAAU;8CAET;mCAbI;;;;;;;;;;;;;;;;;;;;;0BAsBjB,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,UAAU;gCACV,WAAU;0CAET;+BAPI;;;;;;;;;;;;;;;;0BAcb,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA;;sCACE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC;4BACX,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,cAAc,MAAM,KAAK;sCAEnC,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAO5B,4BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,aAAa;;;;;;sCAE5B,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,wCACA,CAAC,qBAAqB,EAAE,aAAa,IAAI,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAQnE", "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/exercises/ReadingGame.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { CheckCircle, XCircle, BookOpen } from 'lucide-react'\nimport { triggerHapticFeedback } from '@/lib/telegram'\n\ninterface Question {\n  question: string\n  options: string[]\n  correct: string\n}\n\ninterface ReadingGameProps {\n  text: string\n  questions: Question[]\n  onComplete: (correct: boolean, score?: number) => void\n}\n\nexport function ReadingGame({ text, questions, onComplete }: ReadingGameProps) {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)\n  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([])\n  const [showResult, setShowResult] = useState(false)\n  const [isCorrect, setIsCorrect] = useState(false)\n  const [showText, setShowText] = useState(true)\n\n  const currentQuestion = questions[currentQuestionIndex]\n  const isLastQuestion = currentQuestionIndex === questions.length - 1\n\n  const handleAnswerSelect = (answer: string) => {\n    if (showResult) return\n    \n    const correct_answer = answer === currentQuestion.correct\n    setIsCorrect(correct_answer)\n    setShowResult(true)\n    \n    // Store the answer\n    const newAnswers = [...selectedAnswers]\n    newAnswers[currentQuestionIndex] = answer\n    setSelectedAnswers(newAnswers)\n    \n    // Trigger haptic feedback\n    triggerHapticFeedback(correct_answer ? 'light' : 'heavy')\n    \n    // Auto-advance after showing result\n    setTimeout(() => {\n      if (isLastQuestion) {\n        // Calculate final score\n        const correctCount = newAnswers.filter((answer, index) => \n          answer === questions[index].correct\n        ).length\n        const score = Math.round((correctCount / questions.length) * 100)\n        onComplete(correctCount > 0, score)\n      } else {\n        // Move to next question\n        setCurrentQuestionIndex(prev => prev + 1)\n        setShowResult(false)\n        setIsCorrect(false)\n      }\n    }, 1500)\n  }\n\n  const toggleTextView = () => {\n    setShowText(!showText)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Понимание прочитанного\n        </h2>\n        <p className=\"text-sm text-gray-600\">\n          Вопрос {currentQuestionIndex + 1} из {questions.length}\n        </p>\n      </div>\n\n      {/* Text Toggle Button */}\n      <div className=\"text-center\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={toggleTextView}\n          className=\"mb-4\"\n        >\n          <BookOpen className=\"h-4 w-4 mr-2\" />\n          {showText ? 'Скрыть текст' : 'Показать текст'}\n        </Button>\n      </div>\n\n      {/* Reading Text */}\n      {showText && (\n        <Card className=\"bg-blue-50 border-blue-200\">\n          <CardHeader className=\"pb-3\">\n            <div className=\"flex items-center space-x-2\">\n              <BookOpen className=\"h-5 w-5 text-blue-600\" />\n              <h3 className=\"font-medium text-blue-900\">Текст для чтения</h3>\n            </div>\n          </CardHeader>\n          <CardContent className=\"pt-0\">\n            <p className=\"text-gray-800 leading-relaxed\">\n              {text}\n            </p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Question */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            {currentQuestion.question}\n          </h3>\n          \n          <div className=\"space-y-3\">\n            {currentQuestion.options.map((option, index) => {\n              let buttonClass = 'w-full text-left justify-start h-auto py-3 px-4'\n              let icon = null\n\n              if (showResult) {\n                if (option === currentQuestion.correct) {\n                  buttonClass += ' bg-green-100 border-green-300 text-green-800'\n                  icon = <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                } else if (option === selectedAnswers[currentQuestionIndex] && option !== currentQuestion.correct) {\n                  buttonClass += ' bg-red-100 border-red-300 text-red-800'\n                  icon = <XCircle className=\"h-5 w-5 text-red-600\" />\n                } else {\n                  buttonClass += ' opacity-50'\n                }\n              } else {\n                buttonClass += ' hover:bg-gray-50 border-gray-300'\n              }\n\n              return (\n                <Button\n                  key={index}\n                  variant=\"outline\"\n                  className={buttonClass}\n                  onClick={() => handleAnswerSelect(option)}\n                  disabled={showResult}\n                >\n                  <div className=\"flex items-center justify-between w-full\">\n                    <span className=\"text-base\">{option}</span>\n                    {icon}\n                  </div>\n                </Button>\n              )\n            })}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Result Message */}\n      {showResult && (\n        <Card className={`${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>\n          <CardContent className=\"p-4 text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              {isCorrect ? (\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-8 w-8 text-red-600\" />\n              )}\n            </div>\n            <h3 className={`font-semibold mb-1 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>\n              {isCorrect ? 'Правильно!' : 'Неправильно'}\n            </h3>\n            <p className={`text-sm ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n              {isCorrect\n                ? isLastQuestion\n                  ? 'Отлично! Вы завершили упражнение по чтению!'\n                  : 'Хорошо! Переходим к следующему вопросу.'\n                : `Правильный ответ: \"${currentQuestion.correct}\".`\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Progress Indicator */}\n      <div className=\"flex justify-center space-x-2\">\n        {questions.map((_, index) => (\n          <div\n            key={index}\n            className={`w-2 h-2 rounded-full ${\n              index < currentQuestionIndex\n                ? 'bg-green-500'\n                : index === currentQuestionIndex\n                  ? 'bg-blue-500'\n                  : 'bg-gray-300'\n            }`}\n          />\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAoBO,SAAS,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAoB;IAC3E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;IACvD,MAAM,iBAAiB,yBAAyB,UAAU,MAAM,GAAG;IAEnE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY;QAEhB,MAAM,iBAAiB,WAAW,gBAAgB,OAAO;QACzD,aAAa;QACb,cAAc;QAEd,mBAAmB;QACnB,MAAM,aAAa;eAAI;SAAgB;QACvC,UAAU,CAAC,qBAAqB,GAAG;QACnC,mBAAmB;QAEnB,0BAA0B;QAC1B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,UAAU;QAEjD,oCAAoC;QACpC,WAAW;YACT,IAAI,gBAAgB;gBAClB,wBAAwB;gBACxB,MAAM,eAAe,WAAW,MAAM,CAAC,CAAC,QAAQ,QAC9C,WAAW,SAAS,CAAC,MAAM,CAAC,OAAO,EACnC,MAAM;gBACR,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,eAAe,UAAU,MAAM,GAAI;gBAC7D,WAAW,eAAe,GAAG;YAC/B,OAAO;gBACL,wBAAwB;gBACxB,wBAAwB,CAAA,OAAQ,OAAO;gBACvC,cAAc;gBACd,aAAa;YACf;QACF,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,YAAY,CAAC;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC3B,uBAAuB;4BAAE;4BAAK,UAAU,MAAM;;;;;;;;;;;;;0BAK1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,WAAW,iBAAiB;;;;;;;;;;;;YAKhC,0BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAG,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAG9C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;0BAOT,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCACX,gBAAgB,QAAQ;;;;;;sCAG3B,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;gCACpC,IAAI,cAAc;gCAClB,IAAI,OAAO;gCAEX,IAAI,YAAY;oCACd,IAAI,WAAW,gBAAgB,OAAO,EAAE;wCACtC,eAAe;wCACf,qBAAO,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;oCAChC,OAAO,IAAI,WAAW,eAAe,CAAC,qBAAqB,IAAI,WAAW,gBAAgB,OAAO,EAAE;wCACjG,eAAe;wCACf,qBAAO,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;oCAC5B,OAAO;wCACL,eAAe;oCACjB;gCACF,OAAO;oCACL,eAAe;gCACjB;gCAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAW;oCACX,SAAS,IAAM,mBAAmB;oCAClC,UAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAa;;;;;;4CAC5B;;;;;;;mCARE;;;;;4BAYX;;;;;;;;;;;;;;;;;YAML,4BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,YAAY,iCAAiC,4BAA4B;0BAC3F,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGvB,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,gBAAgB;sCACjF,YAAY,eAAe;;;;;;sCAE9B,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,mBAAmB,gBAAgB;sCACrE,YACG,iBACE,gDACA,4CACF,CAAC,mBAAmB,EAAE,gBAAgB,OAAO,CAAC,EAAE,CAAC;;;;;;;;;;;;;;;;;0BAQ7D,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,GAAG,sBACjB,8OAAC;wBAEC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,uBACJ,iBACA,UAAU,uBACR,gBACA,eACN;uBAPG;;;;;;;;;;;;;;;;AAajB", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/lesson/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { usePara<PERSON>, useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { ArrowLeft, CheckCircle } from 'lucide-react'\nimport { getExercisesByLessonId, markExerciseComplete, updateUserXP } from '@/lib/database'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Database } from '@/lib/database.types'\nimport { Button } from '@/components/ui/Button'\nimport { ProgressBar } from '@/components/ui/ProgressBar'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { QuizGame } from '@/components/exercises/QuizGame'\nimport { FillInTheBlankGame } from '@/components/exercises/FillInTheBlankGame'\nimport { WordPuzzleGame } from '@/components/exercises/WordPuzzleGame'\nimport { SentenceBuilderGame } from '@/components/exercises/SentenceBuilderGame'\nimport { ReadingGame } from '@/components/exercises/ReadingGame'\n\ntype Exercise = Database['public']['Tables']['exercises']['Row']\n\nexport default function LessonPage() {\n  const params = useParams()\n  const router = useRouter()\n  const { user } = useAuth()\n  const lessonId = params.id as string\n\n  const [exercises, setExercises] = useState<Exercise[]>([])\n  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0)\n  const [loading, setLoading] = useState(true)\n  const [completed, setCompleted] = useState(false)\n  const [totalXP, setTotalXP] = useState(0)\n\n  useEffect(() => {\n    if (lessonId) {\n      loadExercises()\n    }\n  }, [lessonId])\n\n  const loadExercises = async () => {\n    try {\n      setLoading(true)\n      const lessonExercises = await getExercisesByLessonId(lessonId)\n      setExercises(lessonExercises)\n    } catch (error) {\n      console.error('Error loading exercises:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExerciseComplete = async (correct: boolean, score?: number) => {\n    if (!user) return\n\n    const currentExercise = exercises[currentExerciseIndex]\n    \n    if (correct && currentExercise) {\n      // Mark exercise as complete\n      await markExerciseComplete(\n        user.id,\n        lessonId,\n        currentExercise.id,\n        score\n      )\n\n      // Add XP\n      const xpEarned = currentExercise.xp_reward\n      await updateUserXP(user.id, xpEarned)\n      setTotalXP(prev => prev + xpEarned)\n    }\n\n    // Move to next exercise or complete lesson\n    if (currentExerciseIndex < exercises.length - 1) {\n      setCurrentExerciseIndex(prev => prev + 1)\n    } else {\n      setCompleted(true)\n    }\n  }\n\n  const renderExercise = (exercise: Exercise) => {\n    const content = exercise.content_json as any\n\n    switch (exercise.type) {\n      case 'quiz':\n        return (\n          <QuizGame\n            question={content.question}\n            options={content.options}\n            correct={content.correct}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'fill-in-the-blank':\n        return (\n          <FillInTheBlankGame\n            sentence={content.sentence}\n            options={content.options}\n            correct={content.correct}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'word-puzzle':\n        return (\n          <WordPuzzleGame\n            target={content.target}\n            words={content.words}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'sentence-builder':\n        return (\n          <SentenceBuilderGame\n            translation={content.translation}\n            correctOrder={content.correct_order}\n            extraWords={content.extra_words}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      case 'reading':\n        return (\n          <ReadingGame\n            text={content.text}\n            questions={content.questions}\n            onComplete={handleExerciseComplete}\n          />\n        )\n      default:\n        return (\n          <div className=\"text-center py-8\">\n            <p className=\"text-gray-600\">\n              Exercise type \"{exercise.type}\" not implemented yet.\n            </p>\n            <Button \n              onClick={() => handleExerciseComplete(true)}\n              className=\"mt-4\"\n            >\n              Skip Exercise\n            </Button>\n          </div>\n        )\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (exercises.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Упражнения не найдены\n          </h1>\n          <p className=\"text-gray-600 mb-4\">\n            В этом уроке пока нет упражнений.\n          </p>\n          <Link href=\"/lessons\">\n            <Button>Назад к урокам</Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  if (completed) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center max-w-md mx-auto px-4\">\n          <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            Урок завершен!\n          </h1>\n          <p className=\"text-gray-600 mb-4\">\n            Отлично! Вы выполнили все упражнения в этом уроке.\n          </p>\n          <div className=\"bg-white rounded-lg p-4 mb-6\">\n            <div className=\"text-3xl font-bold text-green-600 mb-1\">\n              +{totalXP} XP\n            </div>\n            <div className=\"text-sm text-gray-600\">Опыта получено</div>\n          </div>\n          <div className=\"space-y-3\">\n            <Link href=\"/lessons\">\n              <Button className=\"w-full\">\n                Назад к урокам\n              </Button>\n            </Link>\n            <Link href=\"/\">\n              <Button variant=\"outline\" className=\"w-full\">\n                Главная\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  const currentExercise = exercises[currentExerciseIndex]\n  const progress = ((currentExerciseIndex + 1) / exercises.length) * 100\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-md mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <Link href=\"/lessons\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4\" />\n              </Button>\n            </Link>\n            <div className=\"flex-1\">\n              <h1 className=\"text-lg font-semibold text-gray-900\">\n                Упражнение {currentExerciseIndex + 1} из {exercises.length}\n              </h1>\n            </div>\n          </div>\n          <ProgressBar\n            value={currentExerciseIndex + 1}\n            max={exercises.length}\n            className=\"w-full\"\n          />\n        </div>\n      </div>\n\n      {/* Exercise Content */}\n      <div className=\"max-w-md mx-auto px-4 py-6\">\n        {renderExercise(currentExercise)}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,kBAAkB,MAAM,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE;YACrD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAAO,SAAkB;QACtD,IAAI,CAAC,MAAM;QAEX,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;QAEvD,IAAI,WAAW,iBAAiB;YAC9B,4BAA4B;YAC5B,MAAM,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EACvB,KAAK,EAAE,EACP,UACA,gBAAgB,EAAE,EAClB;YAGF,SAAS;YACT,MAAM,WAAW,gBAAgB,SAAS;YAC1C,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5B,WAAW,CAAA,OAAQ,OAAO;QAC5B;QAEA,2CAA2C;QAC3C,IAAI,uBAAuB,UAAU,MAAM,GAAG,GAAG;YAC/C,wBAAwB,CAAA,OAAQ,OAAO;QACzC,OAAO;YACL,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,SAAS,YAAY;QAErC,OAAQ,SAAS,IAAI;YACnB,KAAK;gBACH,qBACE,8OAAC,2IAAA,CAAA,WAAQ;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,SAAS,QAAQ,OAAO;oBACxB,SAAS,QAAQ,OAAO;oBACxB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,8OAAC,qJAAA,CAAA,qBAAkB;oBACjB,UAAU,QAAQ,QAAQ;oBAC1B,SAAS,QAAQ,OAAO;oBACxB,SAAS,QAAQ,OAAO;oBACxB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,8OAAC,iJAAA,CAAA,iBAAc;oBACb,QAAQ,QAAQ,MAAM;oBACtB,OAAO,QAAQ,KAAK;oBACpB,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,8OAAC,sJAAA,CAAA,sBAAmB;oBAClB,aAAa,QAAQ,WAAW;oBAChC,cAAc,QAAQ,aAAa;oBACnC,YAAY,QAAQ,WAAW;oBAC/B,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,8OAAC,8IAAA,CAAA,cAAW;oBACV,MAAM,QAAQ,IAAI;oBAClB,WAAW,QAAQ,SAAS;oBAC5B,YAAY;;;;;;YAGlB;gBACE,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAgB;gCACX,SAAS,IAAI;gCAAC;;;;;;;sCAEhC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,uBAAuB;4BACtC,WAAU;sCACX;;;;;;;;;;;;QAKT;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAKlB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAyC;oCACpD;oCAAQ;;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAS;;;;;;;;;;;0CAI7B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQzD;IAEA,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;IACvD,MAAM,WAAW,AAAC,CAAC,uBAAuB,CAAC,IAAI,UAAU,MAAM,GAAI;IAEnE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;4CAAsC;4CACtC,uBAAuB;4CAAE;4CAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;sCAIhE,8OAAC,uIAAA,CAAA,cAAW;4BACV,OAAO,uBAAuB;4BAC9B,KAAK,UAAU,MAAM;4BACrB,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACZ,eAAe;;;;;;;;;;;;AAIxB", "debugId": null}}]}