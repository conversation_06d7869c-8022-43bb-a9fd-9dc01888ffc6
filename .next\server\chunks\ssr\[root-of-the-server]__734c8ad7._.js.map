{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/NewHomePage.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { BookOpen, Play, Star, Trophy, User, Settings, Moon, Sun, LogOut } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from '@/contexts/ThemeContext'\nimport { useUser } from '@/contexts/UserContext'\n\ninterface Lesson {\n  id: string\n  title: string\n  level: string\n  order: number\n  description: string\n}\n\ninterface UserType {\n  id: string\n  telegram_id: string\n  nickname: string\n  avatar: string\n  level: string\n  theme: string\n  is_onboarded: boolean\n  total_xp: number\n  current_streak: number\n  last_activity_date: string\n}\n\ninterface NewHomePageProps {\n  user: UserType\n}\n\nexport function NewHomePage({ user }: NewHomePageProps) {\n  const { theme, toggleTheme } = useTheme()\n  const { updateUser, logout } = useUser()\n  const [lessons, setLessons] = useState<Lesson[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [showProfile, setShowProfile] = useState(false)\n\n  useEffect(() => {\n    loadLessons()\n  }, [])\n\n  const loadLessons = async () => {\n    try {\n      const response = await fetch('/api/lessons')\n      const data = await response.json()\n      \n      if (data.lessons) {\n        // Фильтруем уроки по уровню пользователя\n        const userLessons = data.lessons.filter((lesson: Lesson) => lesson.level === user.level)\n        setLessons(userLessons)\n      }\n    } catch (error) {\n      console.error('Error loading lessons:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleThemeToggle = async () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light'\n    toggleTheme()\n    await updateUser({ theme: newTheme })\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-300\">Загружаем уроки...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-md mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"text-2xl\">{user.avatar}</div>\n              <div>\n                <h1 className=\"font-semibold text-gray-900 dark:text-white\">\n                  Привет, {user.nickname}!\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                  Уровень: {user.level} • XP: {user.total_xp}\n                </p>\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleThemeToggle}\n                className=\"p-2\"\n              >\n                {theme === 'light' ? (\n                  <Moon className=\"h-4 w-4\" />\n                ) : (\n                  <Sun className=\"h-4 w-4\" />\n                )}\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowProfile(!showProfile)}\n                className=\"p-2\"\n              >\n                <User className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-md mx-auto p-4 space-y-6\">\n        {/* Profile Card (показывается при клике на User) */}\n        {showProfile && (\n          <Card className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white\">\n            <CardContent className=\"p-6 text-center\">\n              <div className=\"text-4xl mb-3\">{user.avatar}</div>\n              <h2 className=\"text-xl font-bold mb-2\">{user.nickname}</h2>\n              <div className=\"space-y-2 text-sm opacity-90\">\n                <p>Уровень: {user.level}</p>\n                <p>Общий XP: {user.total_xp}</p>\n                <p>Текущая серия: {user.current_streak} дней</p>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={logout}\n                className=\"mt-4 text-white border-white hover:bg-white hover:text-blue-600\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Выйти\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Progress Card */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Твой прогресс\n              </h2>\n              <Trophy className=\"h-5 w-5 text-yellow-500\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-3 gap-4 text-center\">\n              <div>\n                <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                  {user.total_xp}\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-300\">XP</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                  {user.current_streak}\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-300\">Дней подряд</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">\n                  {lessons.length}\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-300\">Уроков</div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Lessons */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Уроки уровня {user.level}\n              </h2>\n              <BookOpen className=\"h-5 w-5 text-blue-500\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            {lessons.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <BookOpen className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n                  Пока нет уроков для уровня {user.level}\n                </p>\n                <Link href=\"/admin\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    Добавить уроки\n                  </Button>\n                </Link>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {lessons.map((lesson, index) => (\n                  <Link key={lesson.id} href={`/lesson/${lesson.id}`}>\n                    <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium\">\n                          {lesson.order}\n                        </div>\n                        <div>\n                          <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                            {lesson.title}\n                          </h3>\n                          <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                            {lesson.description}\n                          </p>\n                        </div>\n                      </div>\n                      <Play className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <Link href=\"/test-memory-match\">\n            <Card className=\"cursor-pointer hover:shadow-md transition-shadow\">\n              <CardContent className=\"p-4 text-center\">\n                <Star className=\"h-8 w-8 text-yellow-500 mx-auto mb-2\" />\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Тест игры\n                </p>\n              </CardContent>\n            </Card>\n          </Link>\n          \n          <Link href=\"/admin\">\n            <Card className=\"cursor-pointer hover:shadow-md transition-shadow\">\n              <CardContent className=\"p-4 text-center\">\n                <Settings className=\"h-8 w-8 text-gray-500 mx-auto mb-2\" />\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Админ панель\n                </p>\n              </CardContent>\n            </Card>\n          </Link>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAmCO,SAAS,YAAY,EAAE,IAAI,EAAoB;IACpD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,yCAAyC;gBACzC,MAAM,cAAc,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,SAAmB,OAAO,KAAK,KAAK,KAAK,KAAK;gBACvF,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C;QACA,MAAM,WAAW;YAAE,OAAO;QAAS;IACrC;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,KAAK,MAAM;;;;;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA8C;oDACjD,KAAK,QAAQ;oDAAC;;;;;;;0DAEzB,8OAAC;gDAAE,WAAU;;oDAA2C;oDAC5C,KAAK,KAAK;oDAAC;oDAAQ,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;0CAMhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;iEAEhB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAInB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1B,8OAAC;gBAAI,WAAU;;oBAEZ,6BACC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CAAiB,KAAK,MAAM;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CAA0B,KAAK,QAAQ;;;;;;8CACrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAU,KAAK,KAAK;;;;;;;sDACvB,8OAAC;;gDAAE;gDAAW,KAAK,QAAQ;;;;;;;sDAC3B,8OAAC;;gDAAE;gDAAgB,KAAK,cAAc;gDAAC;;;;;;;;;;;;;8CAEzC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGtB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ;;;;;;8DAEhB,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACZ,KAAK,cAAc;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,MAAM;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAsD;gDACpD,KAAK,KAAK;;;;;;;sDAE1B,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,8OAAC,gIAAA,CAAA,cAAW;0CACT,QAAQ,MAAM,KAAK,kBAClB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAE,WAAU;;gDAAwC;gDACvB,KAAK,KAAK;;;;;;;sDAExC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAAK;;;;;;;;;;;;;;;;yDAMxC,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAAiB,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;sDAChD,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,OAAO,KAAK;;;;;;0EAEf,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFACX,OAAO,KAAK;;;;;;kFAEf,8OAAC;wEAAE,WAAU;kFACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;kEAIzB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;2CAfT,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCAyB9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;0CAOvE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjF", "debugId": null}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useUser } from '@/contexts/UserContext'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { NewHomePage } from '@/components/NewHomePage'\n\nexport default function Home() {\n  const router = useRouter()\n  const { user, isLoading, isFirstTime } = useUser()\n\n  useEffect(() => {\n    // Симулируем получение telegram_id (в реальном приложении это будет из Telegram WebApp API)\n    if (!localStorage.getItem('telegram_user_id')) {\n      localStorage.setItem('telegram_user_id', Date.now().toString())\n    }\n  }, [])\n\n  // Показываем загрузку\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <LoadingSpinner />\n          <p className=\"text-gray-600 dark:text-gray-300 mt-4\">Загружаем ваш профиль...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Если пользователь новый, перенаправляем на страницу приветствия\n  if (isFirstTime) {\n    router.push('/welcome')\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <LoadingSpinner />\n          <p className=\"text-gray-600 dark:text-gray-300 mt-4\">Перенаправляем...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Если пользователь существует, показываем главную страницу\n  if (user) {\n    return <NewHomePage user={user} />\n  }\n\n  // Fallback\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"text-center\">\n        <h1 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n          Добро пожаловать!\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-300\">\n          Настраиваем ваш аккаунт...\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4FAA4F;QAC5F,IAAI,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC7C,aAAa,OAAO,CAAC,oBAAoB,KAAK,GAAG,GAAG,QAAQ;QAC9D;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,iBAAc;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;;;;;;IAI7D;IAEA,kEAAkE;IAClE,IAAI,aAAa;QACf,OAAO,IAAI,CAAC;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,iBAAc;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;;;;;;IAI7D;IAEA,4DAA4D;IAC5D,IAAI,MAAM;QACR,qBAAO,8OAAC,iIAAA,CAAA,cAAW;YAAC,MAAM;;;;;;IAC5B;IAEA,WAAW;IACX,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2D;;;;;;8BAGzE,8OAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}]}