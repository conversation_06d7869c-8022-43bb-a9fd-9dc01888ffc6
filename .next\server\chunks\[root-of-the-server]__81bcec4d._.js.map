{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/api/users/check-nickname/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nconst supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// GET - проверить доступность никнейма\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const nickname = searchParams.get('nickname')\n    const telegramId = searchParams.get('telegram_id') // Добавляем возможность исключить текущего пользователя\n\n    if (!nickname) {\n      return NextResponse.json({ error: 'nickname is required' }, { status: 400 })\n    }\n\n    // Проверяем длину никнейма\n    if (nickname.length < 3 || nickname.length > 20) {\n      return NextResponse.json({ \n        available: false, \n        error: 'Nickname must be between 3 and 20 characters' \n      })\n    }\n\n    // Проверяем на недопустимые символы\n    const validNickname = /^[a-zA-Z0-9_-]+$/.test(nickname)\n    if (!validNickname) {\n      return NextResponse.json({ \n        available: false, \n        error: 'Nickname can only contain letters, numbers, underscore and dash' \n      })\n    }\n\n    // Проверяем доступность в базе данных (временно используем username)\n    let query = supabase\n      .from('users')\n      .select('id, telegram_id')\n      .eq('username', nickname)\n\n    const { data: existingUsers, error } = await query\n\n    if (error) {\n      console.error('Error checking nickname:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    // Проверяем, есть ли пользователь с таким никнеймом, исключая текущего пользователя\n    const conflictingUser = existingUsers?.find(user =>\n      telegramId ? user.telegram_id.toString() !== telegramId : true\n    )\n\n    const available = !conflictingUser\n    \n    return NextResponse.json({ \n      available,\n      nickname,\n      error: available ? null : 'Nickname is already taken'\n    })\n  } catch (error) {\n    console.error('Error in GET /api/users/check-nickname:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAGpC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,aAAa,aAAa,GAAG,CAAC,eAAe,wDAAwD;;QAE3G,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5E;QAEA,2BAA2B;QAC3B,IAAI,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,IAAI;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,WAAW;gBACX,OAAO;YACT;QACF;QAEA,oCAAoC;QACpC,MAAM,gBAAgB,mBAAmB,IAAI,CAAC;QAC9C,IAAI,CAAC,eAAe;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,WAAW;gBACX,OAAO;YACT;QACF;QAEA,qEAAqE;QACrE,IAAI,QAAQ,SACT,IAAI,CAAC,SACL,MAAM,CAAC,mBACP,EAAE,CAAC,YAAY;QAElB,MAAM,EAAE,MAAM,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM;QAE7C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,oFAAoF;QACpF,MAAM,kBAAkB,eAAe,KAAK,CAAA,OAC1C,aAAa,KAAK,WAAW,CAAC,QAAQ,OAAO,aAAa;QAG5D,MAAM,YAAY,CAAC;QAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA,OAAO,YAAY,OAAO;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}