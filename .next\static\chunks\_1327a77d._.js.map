{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/quick-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\n\nexport default function QuickTestPage() {\n  const router = useRouter()\n  const [telegramId, setTelegramId] = useState<string>('')\n  const [apiStatus, setApiStatus] = useState<string>('Не проверено')\n  const [userExists, setUserExists] = useState<boolean | null>(null)\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    // Получаем текущий telegram_id\n    const currentId = localStorage.getItem('telegram_user_id') || 'Не установлен'\n    setTelegramId(currentId)\n  }, [])\n\n  const testAPI = async () => {\n    setLoading(true)\n    setApiStatus('Проверяем...')\n    \n    try {\n      const response = await fetch(`/api/users?telegram_id=${telegramId}`)\n      const data = await response.json()\n      \n      if (response.ok) {\n        setApiStatus(`✅ API работает (${response.status})`)\n        setUserExists(!!data.user)\n      } else {\n        setApiStatus(`❌ API ошибка (${response.status}): ${data.error || 'Неизвестная ошибка'}`)\n        setUserExists(null)\n      }\n    } catch (error) {\n      setApiStatus(`💥 Ошибка сети: ${error}`)\n      setUserExists(null)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const createNewId = () => {\n    const newId = Date.now().toString()\n    localStorage.setItem('telegram_user_id', newId)\n    setTelegramId(newId)\n    setApiStatus('Не проверено')\n    setUserExists(null)\n  }\n\n  const clearAll = () => {\n    localStorage.clear()\n    setTelegramId('Не установлен')\n    setApiStatus('Не проверено')\n    setUserExists(null)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4\">\n      <div className=\"max-w-2xl mx-auto\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-6\">\n            🧪 Быстрый тест системы\n          </h1>\n\n          {/* Telegram ID */}\n          <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n            <h2 className=\"font-semibold text-blue-900 mb-2\">Telegram ID</h2>\n            <p className=\"text-blue-800 font-mono text-sm break-all\">\n              {telegramId}\n            </p>\n            <div className=\"mt-3 space-x-2\">\n              <button\n                onClick={createNewId}\n                className=\"px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700\"\n              >\n                🆕 Новый ID\n              </button>\n              <button\n                onClick={clearAll}\n                className=\"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700\"\n              >\n                🗑️ Очистить все\n              </button>\n            </div>\n          </div>\n\n          {/* API тест */}\n          <div className=\"mb-6 p-4 bg-green-50 rounded-lg\">\n            <h2 className=\"font-semibold text-green-900 mb-2\">API тест</h2>\n            <p className=\"text-green-800 text-sm mb-3\">\n              Статус: {apiStatus}\n            </p>\n            {userExists !== null && (\n              <p className=\"text-green-800 text-sm mb-3\">\n                Пользователь: {userExists ? '✅ Существует' : '❌ Не найден'}\n              </p>\n            )}\n            <button\n              onClick={testAPI}\n              disabled={loading || telegramId === 'Не установлен'}\n              className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400\"\n            >\n              {loading ? '⏳ Проверяем...' : '🔍 Проверить API'}\n            </button>\n          </div>\n\n          {/* Навигация */}\n          <div className=\"mb-6 p-4 bg-purple-50 rounded-lg\">\n            <h2 className=\"font-semibold text-purple-900 mb-3\">Навигация</h2>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700\"\n              >\n                🏠 Главная\n              </button>\n              <button\n                onClick={() => router.push('/welcome')}\n                className=\"px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700\"\n              >\n                👋 Приветствие\n              </button>\n              <button\n                onClick={() => router.push('/admin')}\n                className=\"px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700\"\n              >\n                ⚙️ Админ\n              </button>\n              <button\n                onClick={() => router.push('/emergency-reset')}\n                className=\"px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700\"\n              >\n                🚨 Сброс\n              </button>\n            </div>\n          </div>\n\n          {/* Системная информация */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <h2 className=\"font-semibold text-gray-900 mb-3\">Системная информация</h2>\n            <div className=\"text-xs text-gray-600 space-y-1\">\n              <p><strong>URL:</strong> {window.location.href}</p>\n              <p><strong>User Agent:</strong> {navigator.userAgent}</p>\n              <p><strong>Время:</strong> {new Date().toLocaleString()}</p>\n              <p><strong>localStorage размер:</strong> {Object.keys(localStorage).length} ключей</p>\n              <p><strong>Доступные ключи:</strong> {Object.keys(localStorage).join(', ') || 'Нет'}</p>\n            </div>\n          </div>\n\n          {/* Действия */}\n          <div className=\"mt-6 flex space-x-2\">\n            <button\n              onClick={() => window.location.reload()}\n              className=\"flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\"\n            >\n              🔄 Обновить страницу\n            </button>\n            <button\n              onClick={() => window.history.back()}\n              className=\"flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\"\n            >\n              ← Назад\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,+BAA+B;YAC/B,MAAM,YAAY,aAAa,OAAO,CAAC,uBAAuB;YAC9D,cAAc;QAChB;kCAAG,EAAE;IAEL,MAAM,UAAU;QACd,WAAW;QACX,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY;YACnE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;gBAClD,cAAc,CAAC,CAAC,KAAK,IAAI;YAC3B,OAAO;gBACL,aAAa,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,sBAAsB;gBACvF,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,gBAAgB,EAAE,OAAO;YACvC,cAAc;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;QACjC,aAAa,OAAO,CAAC,oBAAoB;QACzC,cAAc;QACd,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,WAAW;QACf,aAAa,KAAK;QAClB,cAAc;QACd,aAAa;QACb,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAKtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;;oCAA8B;oCAChC;;;;;;;4BAEV,eAAe,sBACd,6LAAC;gCAAE,WAAU;;oCAA8B;oCAC1B,aAAa,iBAAiB;;;;;;;0CAGjD,6LAAC;gCACC,SAAS;gCACT,UAAU,WAAW,eAAe;gCACpC,WAAU;0CAET,UAAU,mBAAmB;;;;;;;;;;;;kCAKlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAa;4CAAE,OAAO,QAAQ,CAAC,IAAI;;;;;;;kDAC9C,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAoB;4CAAE,UAAU,SAAS;;;;;;;kDACpD,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAe;4CAAE,IAAI,OAAO,cAAc;;;;;;;kDACrD,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAA6B;4CAAE,OAAO,IAAI,CAAC,cAAc,MAAM;4CAAC;;;;;;;kDAC3E,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAyB;4CAAE,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;;;kCAKlF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;gCAClC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnKwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}