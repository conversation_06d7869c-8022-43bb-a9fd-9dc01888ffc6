{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/telegram.ts"], "sourcesContent": ["// For now, we'll use the global Telegram WebApp object\n// import { initData, miniApp } from '@telegram-apps/sdk'\n\nexport interface TelegramUser {\n  id: number\n  first_name: string\n  last_name?: string\n  username?: string\n  language_code?: string\n  is_premium?: boolean\n}\n\nexport interface TelegramInitData {\n  user?: TelegramUser\n  auth_date: number\n  hash: string\n}\n\n// Initialize Telegram WebApp\nexport function initTelegramWebApp() {\n  if (typeof window !== 'undefined') {\n    try {\n      const webApp = window.Telegram?.WebApp\n      if (webApp) {\n        webApp.ready()\n        webApp.expand()\n        webApp.enableClosingConfirmation()\n\n        // Set theme colors\n        webApp.setHeaderColor('#1f2937') // gray-800\n        webApp.setBackgroundColor('#f9fafb') // gray-50\n\n        return true\n      }\n      return false\n    } catch (error) {\n      console.error('Failed to initialize Telegram WebApp:', error)\n      return false\n    }\n  }\n  return false\n}\n\n// Get Telegram user data\nexport function getTelegramUser(): TelegramUser | null {\n  if (typeof window === 'undefined') return null\n\n  try {\n    // Check if we're in Telegram environment\n    if (typeof window !== 'undefined' && window.Telegram?.WebApp?.initDataUnsafe?.user) {\n      console.warn('Using Telegram WebApp data')\n      return window.Telegram.WebApp.initDataUnsafe.user\n    }\n\n    // For development, return a mock user\n    console.warn('Using mock user for development')\n    return {\n      id: 123456789,\n      first_name: 'Test',\n      last_name: 'User',\n      username: 'testuser'\n    }\n  } catch (error) {\n    console.error('Failed to get Telegram user data:', error)\n    // Return mock user as fallback\n    return {\n      id: 123456789,\n      first_name: 'Test',\n      last_name: 'User',\n      username: 'testuser'\n    }\n  }\n}\n\n// Validate Telegram WebApp data (server-side)\nexport function validateTelegramData(initData: string, botToken: string): boolean {\n  if (!initData || !botToken) return false\n  \n  try {\n    const urlParams = new URLSearchParams(initData)\n    const hash = urlParams.get('hash')\n    urlParams.delete('hash')\n    \n    const dataCheckString = Array.from(urlParams.entries())\n      .sort(([a], [b]) => a.localeCompare(b))\n      .map(([key, value]) => `${key}=${value}`)\n      .join('\\n')\n    \n    const crypto = require('crypto')\n    const secretKey = crypto.createHmac('sha256', 'WebAppData').update(botToken).digest()\n    const calculatedHash = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex')\n    \n    return calculatedHash === hash\n  } catch (error) {\n    console.error('Error validating Telegram data:', error)\n    return false\n  }\n}\n\n// Check if running in Telegram WebApp environment\nexport function isTelegramWebApp(): boolean {\n  if (typeof window === 'undefined') return false\n  \n  return !!(\n    window.Telegram?.WebApp ||\n    window.location.search.includes('tgWebAppData') ||\n    window.location.hash.includes('tgWebAppData')\n  )\n}\n\n// Get Telegram WebApp instance\nexport function getTelegramWebApp() {\n  if (typeof window !== 'undefined' && window.Telegram?.WebApp) {\n    return window.Telegram.WebApp\n  }\n  return null\n}\n\n// Show Telegram alert\nexport function showTelegramAlert(message: string) {\n  try {\n    const webApp = window.Telegram?.WebApp\n    if (webApp) {\n      webApp.showAlert(message)\n    } else {\n      alert(message)\n    }\n  } catch (error) {\n    alert(message)\n  }\n}\n\n// Show Telegram confirm dialog\nexport function showTelegramConfirm(message: string, callback: (confirmed: boolean) => void) {\n  try {\n    const webApp = window.Telegram?.WebApp\n    if (webApp) {\n      webApp.showConfirm(message, callback)\n    } else {\n      const confirmed = confirm(message)\n      callback(confirmed)\n    }\n  } catch (error) {\n    const confirmed = confirm(message)\n    callback(confirmed)\n  }\n}\n\n// Haptic feedback\nexport function triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft' = 'light') {\n  try {\n    // Use the miniApp haptic feedback if available\n    if (typeof window !== 'undefined' && window.Telegram?.WebApp?.HapticFeedback) {\n      const haptic = window.Telegram.WebApp.HapticFeedback\n      switch (type) {\n        case 'light':\n          haptic.impactOccurred('light')\n          break\n        case 'medium':\n          haptic.impactOccurred('medium')\n          break\n        case 'heavy':\n          haptic.impactOccurred('heavy')\n          break\n        case 'rigid':\n          haptic.impactOccurred('rigid')\n          break\n        case 'soft':\n          haptic.impactOccurred('soft')\n          break\n      }\n    }\n  } catch (error) {\n    // Silently fail if haptic feedback is not available\n    console.debug('Haptic feedback not available:', error)\n  }\n}\n\n// Close Telegram WebApp\nexport function closeTelegramWebApp() {\n  try {\n    const webApp = window.Telegram?.WebApp\n    if (webApp) {\n      webApp.close()\n    }\n  } catch (error) {\n    console.error('Failed to close Telegram WebApp:', error)\n  }\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;AACvD,yDAAyD;;;;;;;;;;;;AAkBlD,SAAS;IACd,uCAAmC;;IAmBnC;IACA,OAAO;AACT;AAGO,SAAS;IACd,wCAAmC,OAAO;;AA2B5C;AAGO,SAAS,qBAAqB,QAAgB,EAAE,QAAgB;IACrE,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO;IAEnC,IAAI;QACF,MAAM,YAAY,IAAI,gBAAgB;QACtC,MAAM,OAAO,UAAU,GAAG,CAAC;QAC3B,UAAU,MAAM,CAAC;QAEjB,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,OAAO,IACjD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAK,EAAE,aAAa,CAAC,IACnC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EACvC,IAAI,CAAC;QAER,MAAM;QACN,MAAM,YAAY,OAAO,UAAU,CAAC,UAAU,cAAc,MAAM,CAAC,UAAU,MAAM;QACnF,MAAM,iBAAiB,OAAO,UAAU,CAAC,UAAU,WAAW,MAAM,CAAC,iBAAiB,MAAM,CAAC;QAE7F,OAAO,mBAAmB;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAO5C;AAGO,SAAS;IACd,IAAI,gBAAkB,eAAe,OAAO,QAAQ,EAAE,QAAQ;;IAE9D;IACA,OAAO;AACT;AAGO,SAAS,kBAAkB,OAAe;IAC/C,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,EAAE;QAChC,IAAI,QAAQ;YACV,OAAO,SAAS,CAAC;QACnB,OAAO;YACL,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAGO,SAAS,oBAAoB,OAAe,EAAE,QAAsC;IACzF,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,EAAE;QAChC,IAAI,QAAQ;YACV,OAAO,WAAW,CAAC,SAAS;QAC9B,OAAO;YACL,MAAM,YAAY,QAAQ;YAC1B,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,MAAM,YAAY,QAAQ;QAC1B,SAAS;IACX;AACF;AAGO,SAAS,sBAAsB,OAAwD,OAAO;IACnG,IAAI;QACF,+CAA+C;QAC/C,IAAI,gBAAkB,eAAe,OAAO,QAAQ,EAAE,QAAQ,gBAAgB;;QAmB9E;IACF,EAAE,OAAO,OAAO;QACd,oDAAoD;QACpD,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF;AAGO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,EAAE;QAChC,IAAI,QAAQ;YACV,OAAO,KAAK;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;AACF", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key'\n\n// Check if we have valid Supabase configuration\nconst isSupabaseConfigured = supabaseUrl !== 'https://placeholder.supabase.co' &&\n                            supabaseAnonKey !== 'placeholder_key' &&\n                            !supabaseUrl.includes('placeholder') &&\n                            !supabaseAnonKey.includes('placeholder')\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations that require elevated permissions\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder_service_key'\n)\n\nexport { isSupabaseConfigured }\n"], "names": [], "mappings": ";;;;;AAAA;;AAGA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,gDAAgD;AAChD,MAAM,uBAAuB,gBAAgB,qCACjB,oBAAoB,qBACpB,CAAC,YAAY,QAAQ,CAAC,kBACtB,CAAC,gBAAgB,QAAQ,CAAC;AAE/C,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB,IAAI", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/database.ts"], "sourcesContent": ["import { supabase, isSupabaseConfigured } from './supabase'\nimport { Database } from './database.types'\n\ntype User = Database['public']['Tables']['users']['Row']\ntype UserInsert = Database['public']['Tables']['users']['Insert']\ntype Lesson = Database['public']['Tables']['lessons']['Row']\ntype Exercise = Database['public']['Tables']['exercises']['Row']\ntype UserProgress = Database['public']['Tables']['user_progress']['Row']\ntype Flashcard = Database['public']['Tables']['flashcards']['Row']\n\n// User operations\nexport async function createUser(userData: UserInsert): Promise<User | null> {\n  if (!isSupabaseConfigured) {\n    console.warn('Supabase not configured, returning mock user')\n    return {\n      id: 'mock-user-id',\n      telegram_id: userData.telegram_id,\n      username: userData.username,\n      first_name: userData.first_name,\n      last_name: userData.last_name,\n      level: userData.level || 'A1',\n      xp: userData.xp || 0,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  try {\n    // Use the admin client to bypass RLS for user creation\n    const { supabaseAdmin } = await import('./supabase')\n    const { data, error } = await supabaseAdmin\n      .from('users')\n      .insert(userData)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating user:', error)\n      // Return a mock user if creation fails\n      return {\n        id: `user-${userData.telegram_id}`,\n        telegram_id: userData.telegram_id,\n        username: userData.username,\n        first_name: userData.first_name,\n        last_name: userData.last_name,\n        level: userData.level || 'A1',\n        xp: userData.xp || 0,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    }\n\n    return data\n  } catch (error) {\n    console.error('Error in createUser:', error)\n    // Return a mock user as fallback\n    return {\n      id: `user-${userData.telegram_id}`,\n      telegram_id: userData.telegram_id,\n      username: userData.username,\n      first_name: userData.first_name,\n      last_name: userData.last_name,\n      level: userData.level || 'A1',\n      xp: userData.xp || 0,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n}\n\nexport async function getUserByTelegramId(telegramId: number): Promise<User | null> {\n  if (!isSupabaseConfigured) {\n    console.warn('Supabase not configured, returning null (user will be created)')\n    return null\n  }\n\n  try {\n    // Use admin client to bypass RLS issues\n    const { supabaseAdmin } = await import('./supabase')\n    const { data, error } = await supabaseAdmin\n      .from('users')\n      .select('*')\n      .eq('telegram_id', telegramId)\n      .single()\n\n    if (error) {\n      console.error('Error fetching user:', error)\n      return null\n    }\n\n    return data\n  } catch (error) {\n    console.error('Error in getUserByTelegramId:', error)\n    return null\n  }\n}\n\nexport async function updateUserXP(userId: string, xpToAdd: number): Promise<User | null> {\n  if (!isSupabaseConfigured) {\n    console.warn('Supabase not configured, XP update skipped')\n    return null\n  }\n\n  try {\n    // Use admin client to bypass RLS issues\n    const { supabaseAdmin } = await import('./supabase')\n    const { data, error } = await supabaseAdmin\n      .from('users')\n      .update({ xp: supabaseAdmin.sql`xp + ${xpToAdd}` })\n      .eq('id', userId)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user XP:', error)\n      return null\n    }\n\n    return data\n  } catch (error) {\n    console.error('Error in updateUserXP:', error)\n    return null\n  }\n}\n\n// Lesson operations\nexport async function getLessonsByLevel(level: string): Promise<Lesson[]> {\n  const { data, error } = await supabase\n    .from('lessons')\n    .select('*')\n    .eq('level', level)\n    .order('order')\n\n  if (error) {\n    console.error('Error fetching lessons:', error)\n    return []\n  }\n\n  return data || []\n}\n\nexport async function getAllLessons(): Promise<Lesson[]> {\n  if (!isSupabaseConfigured) {\n    console.warn('Supabase not configured, returning mock lessons')\n    return [\n      {\n        id: 'lesson-1',\n        title: 'Basic Greetings',\n        description: 'Learn how to say hello and introduce yourself',\n        level: 'A1',\n        order: 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: 'lesson-2',\n        title: 'Numbers 1-10',\n        description: 'Learn basic numbers from one to ten',\n        level: 'A1',\n        order: 2,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: 'lesson-3',\n        title: 'Present Simple Tense',\n        description: 'Learn how to use present simple tense',\n        level: 'A2',\n        order: 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    ]\n  }\n\n  const { data, error } = await supabase\n    .from('lessons')\n    .select('*')\n    .order('level, order')\n\n  if (error) {\n    console.error('Error fetching all lessons:', error)\n    return []\n  }\n\n  return data || []\n}\n\n// Exercise operations\nexport async function getExercisesByLessonId(lessonId: string): Promise<Exercise[]> {\n  if (!isSupabaseConfigured) {\n    console.warn('Supabase not configured, returning mock exercises')\n    return [\n      {\n        id: 'exercise-1',\n        lesson_id: lessonId,\n        type: 'quiz',\n        content_json: {\n          question: \"How do you greet someone in the morning?\",\n          options: [\"Good morning\", \"Good night\", \"Good afternoon\", \"Good evening\"],\n          correct: \"Good morning\"\n        },\n        xp_reward: 10,\n        order: 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: 'exercise-2',\n        lesson_id: lessonId,\n        type: 'fill-in-the-blank',\n        content_json: {\n          sentence: \"Hello, my name ___ John.\",\n          options: [\"is\", \"are\", \"am\", \"be\"],\n          correct: \"is\"\n        },\n        xp_reward: 10,\n        order: 2,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: 'exercise-3',\n        lesson_id: lessonId,\n        type: 'sentence-builder',\n        content_json: {\n          translation: \"Меня зовут Анна.\",\n          correct_order: [\"My\", \"name\", \"is\", \"Anna\"],\n          extra_words: [\"am\", \"called\", \"the\"]\n        },\n        xp_reward: 15,\n        order: 3,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    ]\n  }\n\n  const { data, error } = await supabase\n    .from('exercises')\n    .select('*')\n    .eq('lesson_id', lessonId)\n    .order('order')\n\n  if (error) {\n    console.error('Error fetching exercises:', error)\n    return []\n  }\n\n  return data || []\n}\n\n// Progress operations\nexport async function getUserProgress(userId: string): Promise<UserProgress[]> {\n  const { data, error } = await supabase\n    .from('user_progress')\n    .select('*')\n    .eq('user_id', userId)\n\n  if (error) {\n    console.error('Error fetching user progress:', error)\n    return []\n  }\n\n  return data || []\n}\n\nexport async function markExerciseComplete(\n  userId: string,\n  lessonId: string,\n  exerciseId: string,\n  score?: number\n): Promise<UserProgress | null> {\n  if (!isSupabaseConfigured) {\n    console.warn('Supabase not configured, progress not saved')\n    return null\n  }\n\n  try {\n    // Use admin client to bypass RLS issues\n    const { supabaseAdmin } = await import('./supabase')\n    const { data, error } = await supabaseAdmin\n      .from('user_progress')\n      .upsert({\n        user_id: userId,\n        lesson_id: lessonId,\n        exercise_id: exerciseId,\n        completed: true,\n        score,\n        completed_at: new Date().toISOString()\n      })\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error marking exercise complete:', error)\n      return null\n    }\n\n    return data\n  } catch (error) {\n    console.error('Error in markExerciseComplete:', error)\n    return null\n  }\n}\n\n// Flashcard operations\nexport async function getFlashcardsByLevel(level: string): Promise<Flashcard[]> {\n  const { data, error } = await supabase\n    .from('flashcards')\n    .select('*')\n    .eq('level', level)\n\n  if (error) {\n    console.error('Error fetching flashcards:', error)\n    return []\n  }\n\n  return data || []\n}\n\n// Level progression logic\nexport function calculateLevel(xp: number): string {\n  if (xp < 100) return 'A1'\n  if (xp < 300) return 'A2'\n  if (xp < 600) return 'B1'\n  if (xp < 1000) return 'B2'\n  if (xp < 1500) return 'C1'\n  return 'C2'\n}\n\nexport function getXPForNextLevel(currentLevel: string): number {\n  const levelThresholds = {\n    'A1': 100,\n    'A2': 300,\n    'B1': 600,\n    'B2': 1000,\n    'C1': 1500,\n    'C2': 2000\n  }\n  return levelThresholds[currentLevel as keyof typeof levelThresholds] || 2000\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAWO,eAAe,WAAW,QAAoB;IACnD,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI;YACJ,aAAa,SAAS,WAAW;YACjC,UAAU,SAAS,QAAQ;YAC3B,YAAY,SAAS,UAAU;YAC/B,WAAW,SAAS,SAAS;YAC7B,OAAO,SAAS,KAAK,IAAI;YACzB,IAAI,SAAS,EAAE,IAAI;YACnB,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,IAAI;QACF,uDAAuD;QACvD,MAAM,EAAE,aAAa,EAAE,GAAG;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,SACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,uCAAuC;YACvC,OAAO;gBACL,IAAI,CAAC,KAAK,EAAE,SAAS,WAAW,EAAE;gBAClC,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,WAAW,SAAS,SAAS;gBAC7B,OAAO,SAAS,KAAK,IAAI;gBACzB,IAAI,SAAS,EAAE,IAAI;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,iCAAiC;QACjC,OAAO;YACL,IAAI,CAAC,KAAK,EAAE,SAAS,WAAW,EAAE;YAClC,aAAa,SAAS,WAAW;YACjC,UAAU,SAAS,QAAQ;YAC3B,YAAY,SAAS,UAAU;YAC/B,WAAW,SAAS,SAAS;YAC7B,OAAO,SAAS,KAAK,IAAI;YACzB,IAAI,SAAS,EAAE,IAAI;YACnB,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;AACF;AAEO,eAAe,oBAAoB,UAAkB;IAC1D,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,wCAAwC;QACxC,MAAM,EAAE,aAAa,EAAE,GAAG;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEO,eAAe,aAAa,MAAc,EAAE,OAAe;IAChE,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,wCAAwC;QACxC,MAAM,EAAE,aAAa,EAAE,GAAG;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,IAAI,cAAc,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;QAAC,GAChD,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAGO,eAAe,kBAAkB,KAAa;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,OACZ,KAAK,CAAC;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe;IACpB,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAGO,eAAe,uBAAuB,QAAgB;IAC3D,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,cAAc;oBACZ,UAAU;oBACV,SAAS;wBAAC;wBAAgB;wBAAc;wBAAkB;qBAAe;oBACzE,SAAS;gBACX;gBACA,WAAW;gBACX,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,cAAc;oBACZ,UAAU;oBACV,SAAS;wBAAC;wBAAM;wBAAO;wBAAM;qBAAK;oBAClC,SAAS;gBACX;gBACA,WAAW;gBACX,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,cAAc;oBACZ,aAAa;oBACb,eAAe;wBAAC;wBAAM;wBAAQ;wBAAM;qBAAO;oBAC3C,aAAa;wBAAC;wBAAM;wBAAU;qBAAM;gBACtC;gBACA,WAAW;gBACX,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAGO,eAAe,gBAAgB,MAAc;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW;IAEjB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe,qBACpB,MAAc,EACd,QAAgB,EAChB,UAAkB,EAClB,KAAc;IAEd,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,wCAAwC;QACxC,MAAM,EAAE,aAAa,EAAE,GAAG;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,SAAS;YACT,WAAW;YACX,aAAa;YACb,WAAW;YACX;YACA,cAAc,IAAI,OAAO,WAAW;QACtC,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe,qBAAqB,KAAa;IACtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS;IAEf,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAGO,SAAS,eAAe,EAAU;IACvC,IAAI,KAAK,KAAK,OAAO;IACrB,IAAI,KAAK,KAAK,OAAO;IACrB,IAAI,KAAK,KAAK,OAAO;IACrB,IAAI,KAAK,MAAM,OAAO;IACtB,IAAI,KAAK,MAAM,OAAO;IACtB,OAAO;AACT;AAEO,SAAS,kBAAkB,YAAoB;IACpD,MAAM,kBAAkB;QACtB,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,OAAO,eAAe,CAAC,aAA6C,IAAI;AAC1E", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { getTelegramUser, initTelegramWebApp, TelegramUser } from '@/lib/telegram'\nimport { createUser, getUserByTelegramId } from '@/lib/database'\nimport { Database } from '@/lib/database.types'\n\ntype User = Database['public']['Tables']['users']['Row']\n\ninterface AuthContextType {\n  user: User | null\n  telegramUser: TelegramUser | null\n  loading: boolean\n  error: string | null\n  login: () => Promise<void>\n  logout: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [telegramUser, setTelegramUser] = useState<TelegramUser | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    initializeAuth()\n  }, [])\n\n  const initializeAuth = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      // Initialize Telegram WebApp\n      const telegramInitialized = initTelegramWebApp()\n      if (!telegramInitialized) {\n        console.warn('Telegram WebApp not initialized - running in development mode')\n      }\n\n      // Get Telegram user data\n      const tgUser = getTelegramUser()\n      if (!tgUser) {\n        // For development, create a mock user\n        const mockUser: TelegramUser = {\n          id: 123456789,\n          first_name: 'Test',\n          last_name: 'User',\n          username: 'testuser'\n        }\n        setTelegramUser(mockUser)\n        await handleUserLogin(mockUser)\n        return\n      }\n\n      setTelegramUser(tgUser)\n      await handleUserLogin(tgUser)\n    } catch (err) {\n      console.error('Auth initialization error:', err)\n      setError('Failed to initialize authentication')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleUserLogin = async (tgUser: TelegramUser) => {\n    try {\n      // Check if user exists in database\n      let dbUser = await getUserByTelegramId(tgUser.id)\n\n      if (!dbUser) {\n        // For new users, we'll create them automatically\n        // In a real app, you might want to redirect to registration page\n        dbUser = await createUser({\n          telegram_id: tgUser.id,\n          username: tgUser.username || null,\n          first_name: tgUser.first_name,\n          last_name: tgUser.last_name || null,\n          level: 'A1',\n          xp: 0\n        })\n      }\n\n      if (dbUser) {\n        setUser(dbUser)\n      } else {\n        throw new Error('Failed to create or retrieve user')\n      }\n    } catch (err) {\n      console.error('User login error:', err)\n      setError('Failed to login user')\n    }\n  }\n\n  const login = async () => {\n    await initializeAuth()\n  }\n\n  const logout = () => {\n    setUser(null)\n    setTelegramUser(null)\n    setError(null)\n  }\n\n  const value: AuthContextType = {\n    user,\n    telegramUser,\n    loading,\n    error,\n    login,\n    logout\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Hook for getting current user with automatic updates\nexport function useUser() {\n  const { user, loading, error } = useAuth()\n  return { user, loading, error }\n}\n\n// Hook for checking if user is authenticated\nexport function useIsAuthenticated() {\n  const { user, loading } = useAuth()\n  return { isAuthenticated: !!user, loading }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,SAAS;YAET,6BAA6B;YAC7B,MAAM,sBAAsB,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD;YAC7C,IAAI,CAAC,qBAAqB;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,yBAAyB;YACzB,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD;YAC7B,IAAI,CAAC,QAAQ;gBACX,sCAAsC;gBACtC,MAAM,WAAyB;oBAC7B,IAAI;oBACJ,YAAY;oBACZ,WAAW;oBACX,UAAU;gBACZ;gBACA,gBAAgB;gBAChB,MAAM,gBAAgB;gBACtB;YACF;YAEA,gBAAgB;YAChB,MAAM,gBAAgB;QACxB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,mCAAmC;YACnC,IAAI,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,EAAE;YAEhD,IAAI,CAAC,QAAQ;gBACX,iDAAiD;gBACjD,iEAAiE;gBACjE,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;oBACxB,aAAa,OAAO,EAAE;oBACtB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,WAAW,OAAO,SAAS,IAAI;oBAC/B,OAAO;oBACP,IAAI;gBACN;YACF;YAEA,IAAI,QAAQ;gBACV,QAAQ;YACV,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qBAAqB;YACnC,SAAS;QACX;IACF;IAEA,MAAM,QAAQ;QACZ,MAAM;IACR;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,gBAAgB;QAChB,SAAS;IACX;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IACjC,OAAO;QAAE;QAAM;QAAS;IAAM;AAChC;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,OAAO;QAAE,iBAAiB,CAAC,CAAC;QAAM;IAAQ;AAC5C", "debugId": null}}]}