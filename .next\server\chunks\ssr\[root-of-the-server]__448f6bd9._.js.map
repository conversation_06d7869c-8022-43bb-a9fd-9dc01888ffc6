{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useUser } from '@/contexts/UserContext'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { BookOpen, Trophy, Target, Play } from 'lucide-react'\n\nexport default function Home() {\n  const router = useRouter()\n  const { user, isLoading, isFirstTime, checkUser } = useUser()\n  const [initialized, setInitialized] = useState(false)\n\n  useEffect(() => {\n    const initializeApp = async () => {\n      try {\n        // Получаем telegram_id из localStorage или создаем новый\n        let telegramId = localStorage.getItem('telegram_user_id')\n        \n        if (!telegramId) {\n          telegramId = Date.now().toString()\n          localStorage.setItem('telegram_user_id', telegramId)\n        }\n\n        console.log('Initializing app with telegram_id:', telegramId)\n        await checkUser(telegramId)\n      } catch (error) {\n        console.error('Error initializing app:', error)\n      } finally {\n        setInitialized(true)\n      }\n    }\n\n    if (!initialized) {\n      initializeApp()\n    }\n  }, [initialized, checkUser])\n\n  // Перенаправляем новых пользователей на регистрацию\n  useEffect(() => {\n    if (initialized && !isLoading && isFirstTime) {\n      console.log('New user detected, redirecting to welcome')\n      router.push('/welcome')\n    }\n  }, [initialized, isLoading, isFirstTime, router])\n\n  // Показываем загрузку\n  if (!initialized || isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <LoadingSpinner />\n          <p className=\"text-gray-600 dark:text-gray-300 mt-4\">Загружаем ваш профиль...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Если пользователь не найден, показываем кнопку для регистрации\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader>\n            <h1 className=\"text-2xl font-bold text-center text-gray-900 dark:text-white\">\n              Добро пожаловать!\n            </h1>\n          </CardHeader>\n          <CardContent className=\"text-center space-y-4\">\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Начните изучение английского языка прямо сейчас\n            </p>\n            <Button \n              onClick={() => router.push('/welcome')}\n              className=\"w-full\"\n            >\n              Начать изучение\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Главная страница для зарегистрированных пользователей\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 p-4\">\n      <div className=\"max-w-md mx-auto space-y-6\">\n        {/* Приветствие пользователя */}\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <div className=\"text-3xl\">{user.avatar}</div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Привет, {user.nickname}!\n                </h1>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Уровень: {user.level}\n                </p>\n              </div>\n            </div>\n            \n            {/* Статистика */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                <Trophy className=\"h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-1\" />\n                <div className=\"text-lg font-bold text-blue-600 dark:text-blue-400\">\n                  {user.total_xp}\n                </div>\n                <div className=\"text-xs text-blue-600 dark:text-blue-400\">XP</div>\n              </div>\n              <div className=\"text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                <Target className=\"h-6 w-6 text-green-600 dark:text-green-400 mx-auto mb-1\" />\n                <div className=\"text-lg font-bold text-green-600 dark:text-green-400\">\n                  {user.current_streak}\n                </div>\n                <div className=\"text-xs text-green-600 dark:text-green-400\">Дней</div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Быстрые действия */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Продолжить изучение\n            </h2>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <Button \n              onClick={() => router.push('/lessons')}\n              className=\"w-full flex items-center justify-center space-x-2\"\n            >\n              <BookOpen className=\"h-4 w-4\" />\n              <span>Уроки</span>\n            </Button>\n            <Button \n              onClick={() => router.push('/test-memory-match')}\n              variant=\"outline\"\n              className=\"w-full flex items-center justify-center space-x-2\"\n            >\n              <Play className=\"h-4 w-4\" />\n              <span>Игры</span>\n            </Button>\n          </CardContent>\n        </Card>\n\n        {/* Дополнительные ссылки */}\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Button \n                onClick={() => router.push('/admin')}\n                variant=\"outline\"\n                size=\"sm\"\n              >\n                Админ\n              </Button>\n              <Button \n                onClick={() => router.push('/quick-test')}\n                variant=\"outline\"\n                size=\"sm\"\n              >\n                Тест\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,yDAAyD;gBACzD,IAAI,aAAa,aAAa,OAAO,CAAC;gBAEtC,IAAI,CAAC,YAAY;oBACf,aAAa,KAAK,GAAG,GAAG,QAAQ;oBAChC,aAAa,OAAO,CAAC,oBAAoB;gBAC3C;gBAEA,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,MAAM,UAAU;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,eAAe;YACjB;QACF;QAEA,IAAI,CAAC,aAAa;YAChB;QACF;IACF,GAAG;QAAC;QAAa;KAAU;IAE3B,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,CAAC,aAAa,aAAa;YAC5C,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAa;QAAW;QAAa;KAAO;IAEhD,sBAAsB;IACtB,IAAI,CAAC,eAAe,WAAW;QAC7B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,iBAAc;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;;;;;;IAI7D;IAEA,iEAAiE;IACjE,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAG,WAAU;sCAA+D;;;;;;;;;;;kCAI/E,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAGhD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,wDAAwD;IACxD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,KAAK,MAAM;;;;;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAkD;oDACrD,KAAK,QAAQ;oDAAC;;;;;;;0DAEzB,8OAAC;gDAAE,WAAU;;oDAAmC;oDACpC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,cAAc;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;sCAItE,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,SAAQ;oCACR,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,SAAQ;oCACR,MAAK;8CACN;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,SAAQ;oCACR,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}