{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/api/exercises/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nconst supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// GET - получить все упражнения\nexport async function GET() {\n  try {\n    const { data: exercises, error } = await supabase\n      .from('exercises')\n      .select(`\n        *,\n        lessons (\n          id,\n          title,\n          level\n        )\n      `)\n      .order('lesson_id, order')\n\n    if (error) {\n      console.error('Error fetching exercises:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ exercises })\n  } catch (error) {\n    console.error('Error in GET /api/exercises:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\n// POST - создать новое упражнение\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { lessonId, type, order, content, xpReward = 10 } = body\n\n    // Валидация\n    if (!lessonId || !type || !order || !content) {\n      return NextResponse.json(\n        { error: 'All fields are required: lessonId, type, order, content' },\n        { status: 400 }\n      )\n    }\n\n    // Валидация JSON контента\n    let parsedContent\n    try {\n      parsedContent = typeof content === 'string' ? JSON.parse(content) : content\n    } catch (error) {\n      return NextResponse.json(\n        { error: 'Invalid JSON format in content' },\n        { status: 400 }\n      )\n    }\n\n    // Проверяем, существует ли урок\n    const { data: lesson, error: lessonError } = await supabase\n      .from('lessons')\n      .select('id')\n      .eq('id', lessonId)\n      .single()\n\n    if (lessonError || !lesson) {\n      return NextResponse.json(\n        { error: 'Lesson not found' },\n        { status: 404 }\n      )\n    }\n\n    // Проверяем, не существует ли уже упражнение с таким порядком в этом уроке\n    const { data: existingExercise } = await supabase\n      .from('exercises')\n      .select('id')\n      .eq('lesson_id', lessonId)\n      .eq('order', order)\n      .single()\n\n    if (existingExercise) {\n      return NextResponse.json(\n        { error: `Exercise with order ${order} already exists for this lesson` },\n        { status: 400 }\n      )\n    }\n\n    // Создаем упражнение\n    const { data: exercise, error } = await supabase\n      .from('exercises')\n      .insert([{\n        lesson_id: lessonId,\n        type,\n        order: parseInt(order),\n        content_json: parsedContent,\n        xp_reward: parseInt(xpReward)\n      }])\n      .select(`\n        *,\n        lessons (\n          id,\n          title,\n          level\n        )\n      `)\n      .single()\n\n    if (error) {\n      console.error('Error creating exercise:', error)\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ exercise }, { status: 201 })\n  } catch (error) {\n    console.error('Error in POST /api/exercises:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAGpC,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,SACtC,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAU;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,GAAG;QAE1D,YAAY;QACZ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0D,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,IAAI;QACJ,IAAI;YACF,gBAAgB,OAAO,YAAY,WAAW,KAAK,KAAK,CAAC,WAAW;QACtE,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,QAAQ;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,2EAA2E;QAC3E,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,SACtC,IAAI,CAAC,aACL,MAAM,CAAC,MACP,EAAE,CAAC,aAAa,UAChB,EAAE,CAAC,SAAS,OACZ,MAAM;QAET,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;YAAC,GACvE;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;gBACP,WAAW;gBACX;gBACA,OAAO,SAAS;gBAChB,cAAc;gBACd,WAAW,SAAS;YACtB;SAAE,EACD,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAS,GAAG;YAAE,QAAQ;QAAI;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}