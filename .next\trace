[{"name": "hot-reloader", "duration": 285, "timestamp": 20919119203, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752345352600, "traceId": "82a510f99c321804"}, {"name": "setup-dev-bundler", "duration": 1485641, "timestamp": 20918502115, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752345351983, "traceId": "82a510f99c321804"}, {"name": "run-instrumentation-hook", "duration": 52, "timestamp": 20920120143, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752345353601, "traceId": "82a510f99c321804"}, {"name": "start-dev-server", "duration": 3039327, "timestamp": 20917180669, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "11546497024", "memory.totalMem": "17177886720", "memory.heapSizeLimit": "8639217664", "memory.rss": "172392448", "memory.heapTotal": "98512896", "memory.heapUsed": "76160712"}, "startTime": 1752345350662, "traceId": "82a510f99c321804"}, {"name": "compile-path", "duration": 5834024, "timestamp": 20943852349, "id": 7, "tags": {"trigger": "/"}, "startTime": 1752345377333, "traceId": "82a510f99c321804"}, {"name": "ensure-page", "duration": 5835881, "timestamp": 20943851584, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1752345377332, "traceId": "82a510f99c321804"}]