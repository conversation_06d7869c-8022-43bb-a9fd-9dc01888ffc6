{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LevelBadge.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LevelBadgeProps {\n  level: string\n  className?: string\n}\n\nconst levelColors = {\n  'A1': 'bg-green-100 text-green-800 border-green-200',\n  'A2': 'bg-blue-100 text-blue-800 border-blue-200',\n  'B1': 'bg-yellow-100 text-yellow-800 border-yellow-200',\n  'B2': 'bg-orange-100 text-orange-800 border-orange-200',\n  'C1': 'bg-red-100 text-red-800 border-red-200',\n  'C2': 'bg-purple-100 text-purple-800 border-purple-200',\n}\n\nexport function LevelBadge({ level, className }: LevelBadgeProps) {\n  const colorClass = levelColors[level as keyof typeof levelColors] || levelColors['A1']\n  \n  return (\n    <span\n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        colorClass,\n        className\n      )}\n    >\n      {level}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,MAAM,aAAa,WAAW,CAAC,MAAkC,IAAI,WAAW,CAAC,KAAK;IAEtF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,YACA;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/ProgressBar.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ProgressBarProps {\n  value: number\n  max: number\n  className?: string\n  showLabel?: boolean\n  label?: string\n}\n\nexport function ProgressBar({ \n  value, \n  max, \n  className, \n  showLabel = false, \n  label \n}: ProgressBarProps) {\n  const percentage = Math.min((value / max) * 100, 100)\n  \n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>{label}</span>\n          <span>{value}/{max}</span>\n        </div>\n      )}\n      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n        <div\n          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,SAAS,YAAY,EAC1B,KAAK,EACL,GAAG,EACH,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACY;IACjB,MAAM,aAAa,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK;IAEjD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM;;;;;;kCACP,8OAAC;;4BAAM;4BAAM;4BAAE;;;;;;;;;;;;;0BAGnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        {\n          'h-4 w-4': size === 'sm',\n          'h-8 w-8': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,WAAW,SAAS;YACpB,WAAW,SAAS;YACpB,aAAa,SAAS;QACxB,GACA;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/HomePage.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { Database } from '@/lib/database.types'\nimport { getAllLessons, calculateLevel, getXPForNextLevel } from '@/lib/database'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { LevelBadge } from '@/components/ui/LevelBadge'\nimport { ProgressBar } from '@/components/ui/ProgressBar'\nimport { Button } from '@/components/ui/Button'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { BookOpen, Trophy, Star } from 'lucide-react'\nimport Link from 'next/link'\n\ntype User = Database['public']['Tables']['users']['Row']\ntype Lesson = Database['public']['Tables']['lessons']['Row']\n\ninterface HomePageProps {\n  user: User\n}\n\nexport function HomePage({ user }: HomePageProps) {\n  const [lessons, setLessons] = useState<Lesson[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadLessons()\n  }, [])\n\n  const loadLessons = async () => {\n    try {\n      const allLessons = await getAllLessons()\n      setLessons(allLessons)\n    } catch (error) {\n      console.error('Error loading lessons:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const currentLevel = calculateLevel(user.xp)\n  const nextLevelXP = getXPForNextLevel(currentLevel)\n  const progressToNextLevel = user.xp % 100 // Simplified progress calculation\n\n  // Group lessons by level\n  const lessonsByLevel = lessons.reduce((acc, lesson) => {\n    if (!acc[lesson.level]) {\n      acc[lesson.level] = []\n    }\n    acc[lesson.level].push(lesson)\n    return acc\n  }, {} as Record<string, Lesson[]>)\n\n  const levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-md mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                Привет, {user.first_name}!\n              </h1>\n              <p className=\"text-sm text-gray-600\">Готов изучать английский?</p>\n            </div>\n            <LevelBadge level={currentLevel} />\n          </div>\n\n          {/* XP Progress */}\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Trophy className=\"h-4 w-4 text-yellow-500\" />\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {user.xp} XP\n                </span>\n              </div>\n              <span className=\"text-xs text-gray-500\">\n                {nextLevelXP - user.xp} XP до следующего уровня\n              </span>\n            </div>\n            <ProgressBar\n              value={progressToNextLevel}\n              max={100}\n              className=\"w-full\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-md mx-auto px-4 py-6\">\n        {loading ? (\n          <div className=\"flex justify-center py-8\">\n            <LoadingSpinner size=\"lg\" />\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            {/* Quick Stats */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <Card>\n                <CardContent className=\"p-4 text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <BookOpen className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    {lessons.length}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">Уроков</div>\n                </CardContent>\n              </Card>\n              <Card>\n                <CardContent className=\"p-4 text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <Star className=\"h-6 w-6 text-yellow-500\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    {user.xp}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">Всего XP</div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Quick Links */}\n            <div className=\"grid grid-cols-2 gap-3 mb-6\">\n              <Link href=\"/register\">\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                  Демо регистрации\n                </Button>\n              </Link>\n              <Link href=\"/how-it-works\">\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                  Как это работает\n                </Button>\n              </Link>\n            </div>\n\n            {/* Lessons by Level */}\n            <div className=\"space-y-4\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                Выберите ваш уровень\n              </h2>\n              \n              {levels.map((level) => {\n                const levelLessons = lessonsByLevel[level] || []\n                const isUnlocked = level <= currentLevel || level === 'A1'\n                \n                return (\n                  <Card\n                    key={level}\n                    className={`${\n                      isUnlocked\n                        ? 'cursor-pointer hover:shadow-md transition-shadow'\n                        : 'opacity-50'\n                    }`}\n                  >\n                    <CardHeader className=\"pb-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-3\">\n                          <LevelBadge level={level} />\n                          <div>\n                            <h3 className=\"font-medium text-gray-900\">\n                              Level {level}\n                            </h3>\n                            <p className=\"text-sm text-gray-600\">\n                              {levelLessons.length} уроков\n                            </p>\n                          </div>\n                        </div>\n                        {isUnlocked ? (\n                          <Link href={`/lessons?level=${level}`}>\n                            <Button size=\"sm\">Начать</Button>\n                          </Link>\n                        ) : (\n                          <Button size=\"sm\" disabled>\n                            Заблокировано\n                          </Button>\n                        )}\n                      </div>\n                    </CardHeader>\n                    {levelLessons.length > 0 && (\n                      <CardContent className=\"pt-0\">\n                        <div className=\"space-y-2\">\n                          {levelLessons.slice(0, 3).map((lesson) => (\n                            <div\n                              key={lesson.id}\n                              className=\"text-sm text-gray-600 flex items-center space-x-2\"\n                            >\n                              <div className=\"w-2 h-2 bg-gray-300 rounded-full\" />\n                              <span>{lesson.title}</span>\n                            </div>\n                          ))}\n                          {levelLessons.length > 3 && (\n                            <div className=\"text-sm text-gray-500\">\n                              +{levelLessons.length - 3} еще уроков\n                            </div>\n                          )}\n                        </div>\n                      </CardContent>\n                    )}\n                  </Card>\n                )\n              })}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAXA;;;;;;;;;;;AAoBO,SAAS,SAAS,EAAE,IAAI,EAAiB;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,aAAa,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;YACrC,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;IACtC,MAAM,sBAAsB,KAAK,EAAE,GAAG,IAAI,kCAAkC;;IAE5E,yBAAyB;IACzB,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC1C,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,EAAE;YACtB,GAAG,CAAC,OAAO,KAAK,CAAC,GAAG,EAAE;QACxB;QACA,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC;QACvB,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,SAAS;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAkC;gDACrC,KAAK,UAAU;gDAAC;;;;;;;sDAE3B,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC,sIAAA,CAAA,aAAU;oCAAC,OAAO;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;;wDACb,KAAK,EAAE;wDAAC;;;;;;;;;;;;;sDAGb,8OAAC;4CAAK,WAAU;;gDACb,cAAc,KAAK,EAAE;gDAAC;;;;;;;;;;;;;8CAG3B,8OAAC,uIAAA,CAAA,cAAW;oCACV,OAAO;oCACP,KAAK;oCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;wBAAC,MAAK;;;;;;;;;;yCAGvB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAG3C,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,EAAE;;;;;;0DAEV,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;8CAIzD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAO3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;gCAInD,OAAO,GAAG,CAAC,CAAC;oCACX,MAAM,eAAe,cAAc,CAAC,MAAM,IAAI,EAAE;oCAChD,MAAM,aAAa,SAAS,gBAAgB,UAAU;oCAEtD,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAW,GACT,aACI,qDACA,cACJ;;0DAEF,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sIAAA,CAAA,aAAU;oEAAC,OAAO;;;;;;8EACnB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;;gFAA4B;gFACjC;;;;;;;sFAET,8OAAC;4EAAE,WAAU;;gFACV,aAAa,MAAM;gFAAC;;;;;;;;;;;;;;;;;;;wDAI1B,2BACC,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,eAAe,EAAE,OAAO;sEACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,MAAK;0EAAK;;;;;;;;;;iFAGpB,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,QAAQ;sEAAC;;;;;;;;;;;;;;;;;4CAMhC,aAAa,MAAM,GAAG,mBACrB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC7B,8OAAC;gEAEC,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;kFAAM,OAAO,KAAK;;;;;;;+DAJd,OAAO,EAAE;;;;;wDAOjB,aAAa,MAAM,GAAG,mBACrB,8OAAC;4DAAI,WAAU;;gEAAwB;gEACnC,aAAa,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;uCA7C/B;;;;;gCAqDX;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { HomePage } from '@/components/HomePage'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\n\nexport default function Home() {\n  const { user, loading, error } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <LoadingSpinner />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <h1 className=\"text-xl font-semibold text-red-600 mb-2\">Ошибка</h1>\n          <p className=\"text-gray-600\">{error}</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <h1 className=\"text-xl font-semibold text-gray-900 mb-2\">Добро пожаловать!</h1>\n          <p className=\"text-gray-600\">Пожалуйста, подождите, пока мы настраиваем ваш аккаунт...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return <HomePage user={user} />\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;;;;;;IAItC;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBAAO,8OAAC,8HAAA,CAAA,WAAQ;QAAC,MAAM;;;;;;AACzB", "debugId": null}}]}