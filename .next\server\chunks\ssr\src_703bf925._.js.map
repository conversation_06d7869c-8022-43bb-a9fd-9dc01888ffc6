{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport function Button({\n  className,\n  variant = 'primary',\n  size = 'md',\n  children,\n  ...props\n}: ButtonProps) {\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        {\n          'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800': variant === 'primary',\n          'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400': variant === 'secondary',\n          'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100': variant === 'outline',\n          'text-gray-700 hover:bg-gray-100 active:bg-gray-200': variant === 'ghost',\n        },\n        {\n          'h-8 px-3 text-sm': size === 'sm',\n          'h-10 px-4 text-base': size === 'md',\n          'h-12 px-6 text-lg': size === 'lg',\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6OACA;YACE,+DAA+D,YAAY;YAC3E,kEAAkE,YAAY;YAC9E,qFAAqF,YAAY;YACjG,sDAAsD,YAAY;QACpE,GACA;YACE,oBAAoB,SAAS;YAC7B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  onClick?: () => void\n}\n\nexport function Card({ children, className, onClick }: CardProps) {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 shadow-sm',\n        onClick && 'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  )\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4', className)}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAa;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,WAAW,oDACX;QAEF,SAAS;kBAER;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/app/test/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\nimport { createUser, getUserByTelegramId, getAllLessons, getExercisesByLessonId } from '@/lib/database'\nimport { CheckCircle, XCircle, Loader } from 'lucide-react'\n\nexport default function TestPage() {\n  const [results, setResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({})\n  const [logs, setLogs] = useState<string[]>([])\n\n  const addLog = (message: string) => {\n    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])\n  }\n\n  const setResult = (test: string, result: 'pending' | 'success' | 'error') => {\n    setResults(prev => ({ ...prev, [test]: result }))\n  }\n\n  const runTest = async (testName: string, testFn: () => Promise<void>) => {\n    setResult(testName, 'pending')\n    addLog(`Starting ${testName}...`)\n    try {\n      await testFn()\n      setResult(testName, 'success')\n      addLog(`✅ ${testName} passed`)\n    } catch (error) {\n      setResult(testName, 'error')\n      addLog(`❌ ${testName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`)\n    }\n  }\n\n  const testUserCreation = async () => {\n    const testUser = {\n      telegram_id: *********,\n      username: 'testuser2',\n      first_name: 'Test',\n      last_name: 'User2',\n      level: 'A1' as const,\n      xp: 0\n    }\n\n    // Try to create user\n    const user = await createUser(testUser)\n    if (!user) throw new Error('Failed to create user')\n    \n    addLog(`User created with ID: ${user.id}`)\n    \n    // Try to fetch user\n    const fetchedUser = await getUserByTelegramId(testUser.telegram_id)\n    if (!fetchedUser) throw new Error('Failed to fetch created user')\n    \n    addLog(`User fetched successfully: ${fetchedUser.first_name}`)\n  }\n\n  const testLessonsLoading = async () => {\n    const lessons = await getAllLessons()\n    if (lessons.length === 0) throw new Error('No lessons found')\n    \n    addLog(`Found ${lessons.length} lessons`)\n    addLog(`First lesson: ${lessons[0].title}`)\n  }\n\n  const testExercisesLoading = async () => {\n    const lessons = await getAllLessons()\n    if (lessons.length === 0) throw new Error('No lessons found')\n    \n    const exercises = await getExercisesByLessonId(lessons[0].id)\n    if (exercises.length === 0) throw new Error('No exercises found')\n    \n    addLog(`Found ${exercises.length} exercises for lesson: ${lessons[0].title}`)\n    addLog(`First exercise type: ${exercises[0].type}`)\n  }\n\n  const runAllTests = async () => {\n    setLogs([])\n    setResults({})\n    \n    await runTest('User Creation', testUserCreation)\n    await runTest('Lessons Loading', testLessonsLoading)\n    await runTest('Exercises Loading', testExercisesLoading)\n    \n    addLog('All tests completed!')\n  }\n\n  const getStatusIcon = (status: 'pending' | 'success' | 'error' | undefined) => {\n    switch (status) {\n      case 'pending':\n        return <Loader className=\"h-5 w-5 text-blue-500 animate-spin\" />\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />\n      default:\n        return <div className=\"h-5 w-5 rounded-full border-2 border-gray-300\" />\n    }\n  }\n\n  const tests = [\n    { name: 'User Creation', key: 'User Creation' },\n    { name: 'Lessons Loading', key: 'Lessons Loading' },\n    { name: 'Exercises Loading', key: 'Exercises Loading' }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">App Testing</h1>\n          <p className=\"text-gray-600\">Test all core functionality</p>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-6 space-y-6\">\n        \n        {/* Test Controls */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold\">Test Controls</h2>\n          </CardHeader>\n          <CardContent>\n            <Button onClick={runAllTests} className=\"w-full\">\n              Run All Tests\n            </Button>\n          </CardContent>\n        </Card>\n\n        {/* Test Results */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold\">Test Results</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {tests.map((test) => (\n                <div key={test.key} className=\"flex items-center space-x-3\">\n                  {getStatusIcon(results[test.key])}\n                  <span className=\"font-medium\">{test.name}</span>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Logs */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold\">Test Logs</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"bg-gray-900 text-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto\">\n              {logs.length === 0 ? (\n                <p className=\"text-gray-400\">No logs yet. Run tests to see output.</p>\n              ) : (\n                logs.map((log, index) => (\n                  <div key={index} className=\"text-sm font-mono\">\n                    {log}\n                  </div>\n                ))\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Quick Links */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold\">Quick Links</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              <Button variant=\"outline\" onClick={() => window.location.href = '/'}>\n                Home\n              </Button>\n              <Button variant=\"outline\" onClick={() => window.location.href = '/lessons?level=A1'}>\n                A1 Lessons\n              </Button>\n              <Button variant=\"outline\" onClick={() => window.location.href = '/setup'}>\n                Setup\n              </Button>\n              <Button variant=\"outline\" onClick={() => window.location.href = '/admin'}>\n                Admin\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD,CAAC;IACzF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7C,MAAM,SAAS,CAAC;QACd,QAAQ,CAAA,OAAQ;mBAAI;gBAAM,GAAG,IAAI,OAAO,kBAAkB,GAAG,EAAE,EAAE,SAAS;aAAC;IAC7E;IAEA,MAAM,YAAY,CAAC,MAAc;QAC/B,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAO,CAAC;IACjD;IAEA,MAAM,UAAU,OAAO,UAAkB;QACvC,UAAU,UAAU;QACpB,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,CAAC;QAChC,IAAI;YACF,MAAM;YACN,UAAU,UAAU;YACpB,OAAO,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC;QAC/B,EAAE,OAAO,OAAO;YACd,UAAU,UAAU;YACpB,OAAO,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC5F;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW;YACf,aAAa;YACb,UAAU;YACV,YAAY;YACZ,WAAW;YACX,OAAO;YACP,IAAI;QACN;QAEA,qBAAqB;QACrB,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;QAC9B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,OAAO,CAAC,sBAAsB,EAAE,KAAK,EAAE,EAAE;QAEzC,oBAAoB;QACpB,MAAM,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,WAAW;QAClE,IAAI,CAAC,aAAa,MAAM,IAAI,MAAM;QAElC,OAAO,CAAC,2BAA2B,EAAE,YAAY,UAAU,EAAE;IAC/D;IAEA,MAAM,qBAAqB;QACzB,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,QAAQ,MAAM,KAAK,GAAG,MAAM,IAAI,MAAM;QAE1C,OAAO,CAAC,MAAM,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;QACxC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;IAC5C;IAEA,MAAM,uBAAuB;QAC3B,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,QAAQ,MAAM,KAAK,GAAG,MAAM,IAAI,MAAM;QAE1C,MAAM,YAAY,MAAM,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE;QAC5D,IAAI,UAAU,MAAM,KAAK,GAAG,MAAM,IAAI,MAAM;QAE5C,OAAO,CAAC,MAAM,EAAE,UAAU,MAAM,CAAC,uBAAuB,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE;IACpD;IAEA,MAAM,cAAc;QAClB,QAAQ,EAAE;QACV,WAAW,CAAC;QAEZ,MAAM,QAAQ,iBAAiB;QAC/B,MAAM,QAAQ,mBAAmB;QACjC,MAAM,QAAQ,qBAAqB;QAEnC,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC;oBAAI,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAiB,KAAK;QAAgB;QAC9C;YAAE,MAAM;YAAmB,KAAK;QAAkB;QAClD;YAAE,MAAM;YAAqB,KAAK;QAAoB;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;0CAExC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAa,WAAU;8CAAS;;;;;;;;;;;;;;;;;kCAOrD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;0CAExC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAAmB,WAAU;;gDAC3B,cAAc,OAAO,CAAC,KAAK,GAAG,CAAC;8DAChC,8OAAC;oDAAK,WAAU;8DAAe,KAAK,IAAI;;;;;;;2CAFhC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;kCAU1B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;0CAExC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;wCAAE,WAAU;kDAAgB;;;;;+CAE7B,KAAK,GAAG,CAAC,CAAC,KAAK,sBACb,8OAAC;4CAAgB,WAAU;sDACxB;2CADO;;;;;;;;;;;;;;;;;;;;;kCAUpB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;0CAExC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sDAAK;;;;;;sDAGrE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sDAAqB;;;;;;sDAGrF,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sDAAU;;;;;;sDAG1E,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxF", "debugId": null}}]}