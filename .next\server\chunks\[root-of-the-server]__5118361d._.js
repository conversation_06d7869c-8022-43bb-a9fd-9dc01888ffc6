module.exports = {

"[project]/.next-internal/server/app/api/users/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/app/api/users/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://qhfixfwfhjqxeqzfmdeg.supabase.co");
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const telegramId = searchParams.get('telegram_id');
        if (!telegramId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'telegram_id is required'
            }, {
                status: 400
            });
        }
        // Преобразуем telegram_id в число, если это возможно
        const telegramIdNum = parseInt(telegramId);
        if (isNaN(telegramIdNum)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid telegram_id format'
            }, {
                status: 400
            });
        }
        const { data: user, error } = await supabase.from('users').select('*').eq('telegram_id', telegramIdNum).single();
        if (error && error.code !== 'PGRST116') {
            console.error('Error fetching user:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message
            }, {
                status: 500
            });
        }
        // Если пользователь найден, форматируем его данные
        if (user) {
            const formattedUser = {
                id: user.id,
                telegram_id: user.telegram_id,
                nickname: user.nickname || user.username || 'User',
                avatar: user.avatar || user.first_name || '👤',
                level: user.level,
                theme: user.theme || 'light',
                is_onboarded: user.is_onboarded || true,
                total_xp: user.total_xp || user.xp || 0,
                current_streak: user.current_streak || 0,
                last_activity_date: user.last_activity_date || new Date().toISOString().split('T')[0],
                created_at: user.created_at,
                updated_at: user.updated_at
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                user: formattedUser
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            user: null
        });
    } catch (error) {
        console.error('Error in GET /api/users:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { telegramId, nickname, avatar, level, theme = 'light' } = body;
        // Валидация
        if (!telegramId || !nickname || !avatar || !level) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'All fields are required: telegramId, nickname, avatar, level'
            }, {
                status: 400
            });
        }
        // Преобразуем telegram_id в число
        const telegramIdNum = parseInt(telegramId);
        if (isNaN(telegramIdNum)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid telegram_id format'
            }, {
                status: 400
            });
        }
        // Проверяем, не существует ли уже пользователь с таким telegram_id
        const { data: existingUser } = await supabase.from('users').select('*').eq('telegram_id', telegramIdNum).single();
        if (existingUser) {
            // Если пользователь существует, обновляем его данные вместо создания нового
            const { data: updatedUser, error: updateError } = await supabase.from('users').update({
                nickname: nickname,
                avatar: avatar,
                level,
                theme: theme || 'light',
                is_onboarded: true,
                total_xp: existingUser.total_xp || existingUser.xp || 0,
                updated_at: new Date().toISOString()
            }).eq('telegram_id', telegramIdNum).select().single();
            if (updateError) {
                console.error('Error updating user:', updateError);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: updateError.message
                }, {
                    status: 500
                });
            }
            // Возвращаем обновленного пользователя в ожидаемом формате
            const formattedUser = {
                id: updatedUser.id,
                telegram_id: updatedUser.telegram_id,
                nickname: updatedUser.nickname,
                avatar: updatedUser.avatar,
                level: updatedUser.level,
                theme: updatedUser.theme,
                is_onboarded: updatedUser.is_onboarded,
                total_xp: updatedUser.total_xp,
                current_streak: updatedUser.current_streak || 0,
                last_activity_date: updatedUser.last_activity_date || new Date().toISOString().split('T')[0],
                created_at: updatedUser.created_at,
                updated_at: updatedUser.updated_at
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                user: formattedUser
            }, {
                status: 200
            });
        }
        // Создаем пользователя с существующими колонками
        const { data: user, error } = await supabase.from('users').insert([
            {
                telegram_id: telegramIdNum,
                nickname: nickname,
                avatar: avatar,
                level,
                theme: theme || 'light',
                is_onboarded: true,
                total_xp: 0,
                current_streak: 0,
                last_activity_date: new Date().toISOString().split('T')[0]
            }
        ]).select().single();
        if (error) {
            console.error('Error creating user:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message
            }, {
                status: 500
            });
        }
        // Возвращаем пользователя в ожидаемом формате
        const formattedUser = {
            id: user.id,
            telegram_id: user.telegram_id,
            nickname: user.nickname,
            avatar: user.avatar,
            level: user.level,
            theme: user.theme,
            is_onboarded: user.is_onboarded,
            total_xp: user.total_xp,
            current_streak: user.current_streak,
            last_activity_date: user.last_activity_date,
            created_at: user.created_at,
            updated_at: user.updated_at
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            user: formattedUser
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error in POST /api/users:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const body = await request.json();
        const { telegramId, nickname, avatar, level, theme, total_xp, ...otherData } = body;
        if (!telegramId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'telegram_id is required'
            }, {
                status: 400
            });
        }
        // Преобразуем telegram_id в число
        const telegramIdNum = parseInt(telegramId);
        if (isNaN(telegramIdNum)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid telegram_id format'
            }, {
                status: 400
            });
        }
        // Маппим новые поля на правильные колонки
        const updateFields = {
            updated_at: new Date().toISOString()
        };
        if (nickname) updateFields.nickname = nickname;
        if (avatar) updateFields.avatar = avatar;
        if (level) updateFields.level = level;
        if (theme) updateFields.theme = theme;
        if (total_xp !== undefined) updateFields.total_xp = total_xp;
        const { data: user, error } = await supabase.from('users').update(updateFields).eq('telegram_id', telegramIdNum).select().single();
        if (error) {
            console.error('Error updating user:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message
            }, {
                status: 500
            });
        }
        // Форматируем ответ
        const formattedUser = {
            id: user.id,
            telegram_id: user.telegram_id,
            nickname: user.nickname,
            avatar: user.avatar,
            level: user.level,
            theme: user.theme || 'light',
            is_onboarded: user.is_onboarded || true,
            total_xp: user.total_xp,
            current_streak: user.current_streak || 0,
            last_activity_date: user.last_activity_date || new Date().toISOString().split('T')[0],
            created_at: user.created_at,
            updated_at: user.updated_at
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            user: formattedUser
        });
    } catch (error) {
        console.error('Error in PUT /api/users:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5118361d._.js.map