{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\n\ntype Theme = 'light' | 'dark'\n\ninterface ThemeContextType {\n  theme: Theme\n  setTheme: (theme: Theme) => void\n  toggleTheme: () => void\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined)\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setTheme] = useState<Theme>('light')\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n    // Загружаем тему из localStorage или системных настроек\n    const savedTheme = localStorage.getItem('theme') as Theme\n    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'\n    \n    setTheme(savedTheme || systemTheme)\n  }, [])\n\n  useEffect(() => {\n    if (!mounted) return\n\n    // Применяем тему к документу\n    document.documentElement.classList.remove('light', 'dark')\n    document.documentElement.classList.add(theme)\n    \n    // Сохраняем в localStorage\n    localStorage.setItem('theme', theme)\n  }, [theme, mounted])\n\n  const toggleTheme = () => {\n    setTheme(prev => prev === 'light' ? 'dark' : 'light')\n  }\n\n  // Предотвращаем гидратацию до монтирования\n  if (!mounted) {\n    return <div className=\"min-h-screen bg-white\">{children}</div>\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  )\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext)\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,wDAAwD;QACxD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;QAEzF,SAAS,cAAc;IACzB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,6BAA6B;QAC7B,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS;QACnD,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QAEvC,2BAA2B;QAC3B,aAAa,OAAO,CAAC,SAAS;IAChC,GAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,SAAS,CAAA,OAAQ,SAAS,UAAU,SAAS;IAC/C;IAEA,2CAA2C;IAC3C,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,WAAU;sBAAyB;;;;;;IACjD;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAY;kBAC1D;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/src/contexts/UserContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\n\ninterface User {\n  id: string\n  telegram_id: string\n  nickname: string\n  avatar: string\n  level: string\n  theme: string\n  is_onboarded: boolean\n  total_xp: number\n  current_streak: number\n  last_activity_date: string\n  created_at: string\n  updated_at: string\n}\n\ninterface UserContextType {\n  user: User | null\n  isLoading: boolean\n  isFirstTime: boolean\n  setUser: (user: User | null) => void\n  updateUser: (updates: Partial<User>) => Promise<void>\n  checkUser: (telegramId: string) => Promise<void>\n  logout: () => void\n}\n\nconst UserContext = createContext<UserContextType | undefined>(undefined)\n\nexport function UserProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [isFirstTime, setIsFirstTime] = useState(false)\n\n  const checkUser = async (telegramId: string) => {\n    setIsLoading(true)\n    try {\n      const response = await fetch(`/api/users?telegram_id=${telegramId}`)\n      const data = await response.json()\n      \n      if (data.user) {\n        setUser(data.user)\n        setIsFirstTime(false)\n      } else {\n        setUser(null)\n        setIsFirstTime(true)\n      }\n    } catch (error) {\n      console.error('Error checking user:', error)\n      setUser(null)\n      setIsFirstTime(true)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const updateUser = async (updates: Partial<User>) => {\n    if (!user) return\n\n    try {\n      const response = await fetch('/api/users', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          telegramId: user.telegram_id,\n          ...updates\n        }),\n      })\n\n      const data = await response.json()\n      \n      if (response.ok) {\n        setUser(data.user)\n      } else {\n        console.error('Error updating user:', data.error)\n      }\n    } catch (error) {\n      console.error('Error updating user:', error)\n    }\n  }\n\n  const logout = () => {\n    setUser(null)\n    setIsFirstTime(false)\n    localStorage.removeItem('telegram_user_id')\n  }\n\n  // Проверяем пользователя при загрузке\n  useEffect(() => {\n    const telegramId = localStorage.getItem('telegram_user_id')\n    if (telegramId) {\n      checkUser(telegramId)\n    } else {\n      setIsLoading(false)\n      setIsFirstTime(true)\n    }\n  }, [])\n\n  return (\n    <UserContext.Provider value={{\n      user,\n      isLoading,\n      isFirstTime,\n      setUser,\n      updateUser,\n      checkUser,\n      logout\n    }}>\n      {children}\n    </UserContext.Provider>\n  )\n}\n\nexport function useUser() {\n  const context = useContext(UserContext)\n  if (context === undefined) {\n    throw new Error('useUser must be used within a UserProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA6BA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,YAAY,OAAO;QACvB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY;YACnE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,KAAK,IAAI;gBACjB,eAAe;YACjB,OAAO;gBACL,QAAQ;gBACR,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,QAAQ;YACR,eAAe;QACjB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,KAAK,WAAW;oBAC5B,GAAG,OAAO;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,QAAQ,KAAK,CAAC,wBAAwB,KAAK,KAAK;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,eAAe;QACf,aAAa,UAAU,CAAC;IAC1B;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,UAAU;QACZ,OAAO;YACL,aAAa;YACb,eAAe;QACjB;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mini_app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}