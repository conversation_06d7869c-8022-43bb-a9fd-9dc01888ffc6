'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

interface User {
  id: string
  telegram_id: string
  nickname: string
  avatar: string
  level: string
  theme: string
  is_onboarded: boolean
  total_xp: number
  current_streak: number
  last_activity_date: string
  created_at: string
  updated_at: string
}

interface UserContextType {
  user: User | null
  isLoading: boolean
  isFirstTime: boolean
  setUser: (user: User | null) => void
  updateUser: (updates: Partial<User>) => Promise<void>
  checkUser: (telegramId: string) => Promise<void>
  logout: () => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isFirstTime, setIsFirstTime] = useState(false)

  const checkUser = async (telegramId: string) => {
    console.log('🔍 UserContext: Starting checkUser with ID:', telegramId)
    setIsLoading(true)

    // Устанавливаем таймаут для предотвращения зависания
    const timeoutId = setTimeout(() => {
      console.log('⏰ UserContext: Timeout reached, treating as new user')
      setUser(null)
      setIsFirstTime(true)
      setIsLoading(false)
    }, 5000) // 5 секунд максимум

    try {
      // Быстрая валидация
      if (!telegramId || telegramId === 'null' || telegramId === 'undefined' || telegramId.length < 5) {
        console.log('❌ UserContext: Invalid telegram_id, treating as new user')
        clearTimeout(timeoutId)
        setUser(null)
        setIsFirstTime(true)
        setIsLoading(false)
        return
      }

      console.log('🌐 UserContext: Making API request...')
      const controller = new AbortController()
      const timeoutSignal = setTimeout(() => controller.abort(), 3000) // 3 секунды на запрос

      const response = await fetch(`/api/users?telegram_id=${telegramId}`, {
        signal: controller.signal
      })

      clearTimeout(timeoutSignal)
      clearTimeout(timeoutId)

      if (!response.ok) {
        console.log('❌ UserContext: API response not ok:', response.status)
        setUser(null)
        setIsFirstTime(true)
        setIsLoading(false)
        return
      }

      const data = await response.json()
      console.log('✅ UserContext: API response:', data)

      if (data.user) {
        console.log('👤 UserContext: User found, setting user')
        setUser(data.user)
        setIsFirstTime(false)
      } else {
        console.log('🆕 UserContext: User not found, treating as new user')
        setUser(null)
        setIsFirstTime(true)
      }

    } catch (error) {
      console.error('💥 UserContext: Error checking user:', error)
      clearTimeout(timeoutId)
      setUser(null)
      setIsFirstTime(true)
    } finally {
      setIsLoading(false)
      console.log('🏁 UserContext: checkUser completed')
    }
  }

  // Переопределяем setUser для добавления логики
  const setUserWithLogging = (newUser: User | null) => {
    console.log('Setting user:', newUser)
    setUser(newUser)
    if (newUser) {
      console.log('User set, updating isFirstTime to false')
      setIsFirstTime(false)
    }
  }

  const updateUser = async (updates: Partial<User>) => {
    if (!user) return

    try {
      const response = await fetch('/api/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          telegramId: user.telegram_id,
          ...updates
        }),
      })

      const data = await response.json()
      
      if (response.ok) {
        setUser(data.user)
      } else {
        console.error('Error updating user:', data.error)
      }
    } catch (error) {
      console.error('Error updating user:', error)
    }
  }

  const logout = () => {
    setUser(null)
    setIsFirstTime(false)
    localStorage.removeItem('telegram_user_id')
  }

  // Проверяем пользователя при загрузке
  useEffect(() => {
    const telegramId = localStorage.getItem('telegram_user_id')
    if (telegramId) {
      checkUser(telegramId)
    } else {
      setIsLoading(false)
      setIsFirstTime(true)
    }
  }, [])

  return (
    <UserContext.Provider value={{
      user,
      isLoading,
      isFirstTime,
      setUser: setUserWithLogging,
      updateUser,
      checkUser,
      logout
    }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
