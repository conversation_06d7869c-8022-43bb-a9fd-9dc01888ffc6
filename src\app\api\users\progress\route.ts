import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// GET - получить прогресс пользователя
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const telegramId = searchParams.get('telegram_id')

    if (!telegramId) {
      return NextResponse.json({ error: 'telegram_id is required' }, { status: 400 })
    }

    // Получаем пользователя
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', parseInt(telegramId) || telegramId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Получаем все уроки для уровня пользователя
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('*')
      .eq('level', user.level)
      .order('order')

    if (lessonsError) {
      console.error('Error fetching lessons:', lessonsError)
      return NextResponse.json({ error: lessonsError.message }, { status: 500 })
    }

    // Получаем прогресс пользователя
    const { data: progress, error: progressError } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', user.id)

    if (progressError) {
      console.error('Error fetching progress:', progressError)
      // Если таблица прогресса не существует, возвращаем пустой прогресс
      const progressData = {
        user: {
          id: user.id,
          telegram_id: user.telegram_id,
          nickname: user.username || 'User',
          avatar: user.first_name || '👤',
          level: user.level,
          theme: 'light',
          is_onboarded: true,
          total_xp: user.xp || 0,
          current_streak: 0,
          last_activity_date: new Date().toISOString().split('T')[0],
          created_at: user.created_at,
          updated_at: user.updated_at
        },
        lessons: lessons || [],
        progress: [],
        stats: {
          totalLessons: lessons?.length || 0,
          completedLessons: 0,
          totalXP: user.xp || 0,
          currentStreak: 0,
          nextLesson: lessons?.[0] || null
        }
      }
      return NextResponse.json(progressData)
    }

    // Подсчитываем статистику
    const completedLessons = progress?.filter(p => p.completed).length || 0
    const nextLesson = lessons?.find(lesson => 
      !progress?.some(p => p.lesson_id === lesson.id && p.completed)
    ) || null

    const progressData = {
      user: {
        id: user.id,
        telegram_id: user.telegram_id,
        nickname: user.username || 'User',
        avatar: user.first_name || '👤',
        level: user.level,
        theme: 'light',
        is_onboarded: true,
        total_xp: user.xp || 0,
        current_streak: 0,
        last_activity_date: new Date().toISOString().split('T')[0],
        created_at: user.created_at,
        updated_at: user.updated_at
      },
      lessons: lessons || [],
      progress: progress || [],
      stats: {
        totalLessons: lessons?.length || 0,
        completedLessons,
        totalXP: user.xp || 0,
        currentStreak: 0,
        nextLesson
      }
    }

    return NextResponse.json(progressData)
  } catch (error) {
    console.error('Error in GET /api/users/progress:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
