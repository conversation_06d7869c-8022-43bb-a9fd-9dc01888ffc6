'use client'

import React, { useState } from 'react'
import { MemoryMatchGame } from '@/components/exercises/MemoryMatchGame'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'
import { ArrowLeft, RotateCcw } from 'lucide-react'

export default function TestMemoryMatchPage() {
  const [gameKey, setGameKey] = useState(0)
  const [selectedLevel, setSelectedLevel] = useState('A1')

  const wordPairsByLevel = {
    'A1': [
      { english: "Hello", russian: "Привет" },
      { english: "Good morning", russian: "Доброе утро" },
      { english: "Thank you", russian: "Спасибо" },
      { english: "Please", russian: "Пожалуйста" },
      { english: "Goodbye", russian: "До свидания" },
      { english: "Yes", russian: "Да" }
    ],
    'A2': [
      { english: "Family", russian: "Семья" },
      { english: "House", russian: "Дом" },
      { english: "School", russian: "Школа" },
      { english: "Friend", russian: "Друг" },
      { english: "Work", russian: "Работа" },
      { english: "Food", russian: "Еда" }
    ],
    'B1': [
      { english: "Experience", russian: "Опыт" },
      { english: "Important", russian: "Важный" },
      { english: "Difficult", russian: "Трудный" },
      { english: "Interesting", russian: "Интересный" },
      { english: "Beautiful", russian: "Красивый" },
      { english: "Successful", russian: "Успешный" }
    ]
  }

  const timeLimitByLevel = {
    'A1': 150,
    'A2': 140,
    'B1': 130
  }

  const handleGameComplete = (correct: boolean, score?: number) => {
    console.log('Game completed:', { correct, score })
    const message = correct
      ? `🎉 Отлично! Вы нашли все пары!\nСчёт: ${score || 0} очков`
      : `⏰ Время вышло!\nПопробуйте еще раз!`
    alert(message)
  }

  const resetGame = () => {
    setGameKey(prev => prev + 1)
  }

  const changeLevel = (level: string) => {
    setSelectedLevel(level)
    setGameKey(prev => prev + 1)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link
            href="/"
            className="flex items-center justify-center w-10 h-10 rounded-full bg-white shadow-sm hover:shadow-md transition-shadow"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <h1 className="text-xl font-semibold text-gray-900">
            Тест игры Memory Match
          </h1>
        </div>

        {/* Level Selector */}
        <Card className="mb-4">
          <CardContent className="p-4">
            <h3 className="font-medium text-gray-800 mb-3">Выберите уровень:</h3>
            <div className="flex gap-2">
              {Object.keys(wordPairsByLevel).map((level) => (
                <Button
                  key={level}
                  variant={selectedLevel === level ? "default" : "outline"}
                  size="sm"
                  onClick={() => changeLevel(level)}
                  className="flex-1"
                >
                  {level}
                </Button>
              ))}
            </div>
            <div className="mt-2 text-sm text-gray-600 text-center">
              Время: {timeLimitByLevel[selectedLevel as keyof typeof timeLimitByLevel]} сек
            </div>
          </CardContent>
        </Card>

        {/* Game Controls */}
        <div className="flex justify-center mb-4">
          <Button
            variant="outline"
            onClick={resetGame}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Перезапустить игру
          </Button>
        </div>

        {/* Game Card */}
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <h2 className="text-lg font-medium text-gray-800">
              Игра на сопоставление слов - {selectedLevel}
            </h2>
            <p className="text-sm text-gray-600">
              Найдите пары английских и русских слов
            </p>
          </CardHeader>
          <CardContent>
            <MemoryMatchGame
              key={gameKey}
              wordPairs={wordPairsByLevel[selectedLevel as keyof typeof wordPairsByLevel]}
              timeLimit={timeLimitByLevel[selectedLevel as keyof typeof timeLimitByLevel]}
              onComplete={handleGameComplete}
            />
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card className="mt-6 bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <h3 className="font-medium text-blue-900 mb-2">Как играть:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Нажмите "Начать игру" для старта</li>
              <li>• Выберите карточку с английским словом</li>
              <li>• Затем выберите соответствующий русский перевод</li>
              <li>• Правильные пары станут зелеными и исчезнут</li>
              <li>• Неправильные пары станут красными</li>
              <li>• Найдите все пары до окончания времени!</li>
            </ul>
            <div className="mt-3 pt-3 border-t border-blue-200">
              <h4 className="font-medium text-blue-900 mb-1">Уровни сложности:</h4>
              <div className="text-xs text-blue-700 space-y-1">
                <div>• A1: Базовые слова (150 сек)</div>
                <div>• A2: Повседневные слова (140 сек)</div>
                <div>• B1: Сложные слова (130 сек)</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
