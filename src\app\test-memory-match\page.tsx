'use client'

import React from 'react'
import { MemoryMatchGame } from '@/components/exercises/MemoryMatchGame'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'

export default function TestMemoryMatchPage() {
  const testWordPairs = [
    { english: "Hello", russian: "Привет" },
    { english: "Good morning", russian: "Доброе утро" },
    { english: "Thank you", russian: "Спасибо" },
    { english: "Please", russian: "Пожалуйста" },
    { english: "Goodbye", russian: "До свидания" },
    { english: "Yes", russian: "Да" }
  ]

  const handleGameComplete = (correct: boolean, score?: number) => {
    console.log('Game completed:', { correct, score })
    alert(`Игра завершена! ${correct ? 'Успешно' : 'Неуспешно'}. Счёт: ${score || 0}`)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link 
            href="/"
            className="flex items-center justify-center w-10 h-10 rounded-full bg-white shadow-sm hover:shadow-md transition-shadow"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <h1 className="text-xl font-semibold text-gray-900">
            Тест игры Memory Match
          </h1>
        </div>

        {/* Game Card */}
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <h2 className="text-lg font-medium text-gray-800">
              Игра на сопоставление слов
            </h2>
            <p className="text-sm text-gray-600">
              Найдите пары английских и русских слов
            </p>
          </CardHeader>
          <CardContent>
            <MemoryMatchGame
              wordPairs={testWordPairs}
              timeLimit={120}
              onComplete={handleGameComplete}
            />
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card className="mt-6 bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <h3 className="font-medium text-blue-900 mb-2">Как играть:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Нажмите "Начать игру" для старта</li>
              <li>• Выберите карточку с английским словом</li>
              <li>• Затем выберите соответствующий русский перевод</li>
              <li>• Правильные пары станут зелеными и исчезнут</li>
              <li>• Неправильные пары станут красными</li>
              <li>• Найдите все пары до окончания времени!</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
