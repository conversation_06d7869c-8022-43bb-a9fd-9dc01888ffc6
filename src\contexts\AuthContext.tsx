'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getTelegramUser, initTelegramWebApp, TelegramUser } from '@/lib/telegram'
import { createUser, getUserByTelegramId } from '@/lib/database'
import { Database } from '@/lib/database.types'

type User = Database['public']['Tables']['users']['Row']

interface AuthContextType {
  user: User | null
  telegramUser: TelegramUser | null
  loading: boolean
  error: string | null
  login: () => Promise<void>
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [telegramUser, setTelegramUser] = useState<TelegramUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      setLoading(true)
      setError(null)

      // Initialize Telegram WebApp
      const telegramInitialized = initTelegramWebApp()
      if (!telegramInitialized) {
        console.warn('Telegram WebApp not initialized - running in development mode')
      }

      // Get Telegram user data
      const tgUser = getTelegramUser()
      if (!tgUser) {
        // For development, create a mock user
        const mockUser: TelegramUser = {
          id: 123456789,
          first_name: 'Test',
          last_name: 'User',
          username: 'testuser'
        }
        setTelegramUser(mockUser)
        await handleUserLogin(mockUser)
        return
      }

      setTelegramUser(tgUser)
      await handleUserLogin(tgUser)
    } catch (err) {
      console.error('Auth initialization error:', err)
      setError('Failed to initialize authentication')
    } finally {
      setLoading(false)
    }
  }

  const handleUserLogin = async (tgUser: TelegramUser) => {
    try {
      // Check if user exists in database
      let dbUser = await getUserByTelegramId(tgUser.id)

      if (!dbUser) {
        // For new users, we'll create them automatically
        // In a real app, you might want to redirect to registration page
        dbUser = await createUser({
          telegram_id: tgUser.id,
          username: tgUser.username || null,
          first_name: tgUser.first_name,
          last_name: tgUser.last_name || null,
          level: 'A1',
          xp: 0
        })
      }

      if (dbUser) {
        setUser(dbUser)
      } else {
        throw new Error('Failed to create or retrieve user')
      }
    } catch (err) {
      console.error('User login error:', err)
      setError('Failed to login user')
    }
  }

  const login = async () => {
    await initializeAuth()
  }

  const logout = () => {
    setUser(null)
    setTelegramUser(null)
    setError(null)
  }

  const value: AuthContextType = {
    user,
    telegramUser,
    loading,
    error,
    login,
    logout
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for getting current user with automatic updates
export function useUser() {
  const { user, loading, error } = useAuth()
  return { user, loading, error }
}

// Hook for checking if user is authenticated
export function useIsAuthenticated() {
  const { user, loading } = useAuth()
  return { isAuthenticated: !!user, loading }
}
